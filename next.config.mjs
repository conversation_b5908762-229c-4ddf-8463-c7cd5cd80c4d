/** @type {import('next').NextConfig} */
const nextConfig = {
  /* Explicitly disable internationalization */
  i18n: null,

  /* Ensure the app only uses English */
  headers: async () => {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Content-Language',
            value: 'en',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
