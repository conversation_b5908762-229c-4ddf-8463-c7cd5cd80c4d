import { AllAgentConfigsType } from "@/app/types";
import frontDeskAuthentication from "./frontDeskAuthentication";
import customerServiceRetail from "./customerServiceRetail";
import simpleExample from "./simpleExample";
import interviewExpert from "./interviewexpert/JavaInterviewExpert";
import javaSpringInterviewExpert from "./interviewexpert/javaSpringInterviewExpert";
import highPerformanceJavaExpert from "./interviewexpert/highPerformanceJavaExpert";

export const allAgentSets: AllAgentConfigsType = {
  frontDeskAuthentication,
  customerServiceRetail,
  simpleExample,
  javaInterviewExpert: interviewExpert,
  javaSpringInterviewExpert,
  highPerformanceJavaExpert,
};

export const defaultAgentSetKey = "highPerformanceJavaExpert";
