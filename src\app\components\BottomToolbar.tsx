"use client";

import React from "react";
import dynamic from "next/dynamic";
import { SessionStatus } from "@/app/types";

// Import the client-side only component with dynamic import
const ClientBottomToolbar = dynamic(() => import("./ClientBottomToolbar"), {
  ssr: false,
  loading: () => (
    <div className="p-4 flex flex-row items-center justify-center">
      <div className="text-gray-500">Loading toolbar...</div>
    </div>
  ),
});

interface BottomToolbarProps {
  sessionStatus: SessionStatus;
  onToggleConnection: () => void;
  isPTTActive: boolean;
  setIsPTTActive: (val: boolean) => void;
  isPTTUserSpeaking: boolean;
  handleTalkButtonDown: () => void;
  handleTalkButtonUp: () => void;
  isEventsPaneExpanded: boolean;
  setIsEventsPaneExpanded: (val: boolean) => void;
  isAudioPlaybackEnabled: boolean;
  setIsAudioPlaybackEnabled: (val: boolean) => void;
  codec: string;
  onCodecChange: (newCodec: string) => void;
  isListeningPaused?: boolean;
}

function BottomToolbar(props: BottomToolbarProps) {
  return <ClientBottomToolbar {...props} />;
}

export default BottomToolbar;
