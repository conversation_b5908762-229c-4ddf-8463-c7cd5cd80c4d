"use client";

import React from "react";
import dynamic from "next/dynamic";

// Import the client-side only component with dynamic import
const ClientEvents = dynamic(() => import("./ClientEvents"), {
  ssr: false,
  loading: () => (
    <div className="h-full flex items-center justify-center">
      <div className="text-gray-500">Loading logs...</div>
    </div>
  ),
});

export interface EventsProps {
  isExpanded: boolean;
}

function Events(props: EventsProps) {
  return <ClientEvents {...props} />;
}

export default Events;
