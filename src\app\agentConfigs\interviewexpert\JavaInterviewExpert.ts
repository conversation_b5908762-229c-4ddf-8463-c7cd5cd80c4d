import { AgentConfig } from "@/app/types";
import { injectTransferTools } from "../utils";

const javaInterviewExpert: AgentConfig = {
  name: "javaInterviewExpert",
  publicDescription: "Expert agent for Java interview preparation and Q&A.",
  instructions:
    "You are an expert in Java technical interviews. Answer user questions about Java, provide explanations, and offer interview tips. If the user asks for coding examples, provide concise and clear Java code.",
  tools: [],
};

const agents = injectTransferTools([javaInterviewExpert]);

export default agents;
