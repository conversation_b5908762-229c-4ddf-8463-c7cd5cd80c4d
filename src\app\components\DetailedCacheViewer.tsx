"use client";

import React, { useState } from "react";

interface CacheEntry {
  value: any;
  timestamp: number;
  formattedDate: string;
  expiresIn: number;
}

interface DetailedCacheViewerProps {
  cacheContents: Record<string, CacheEntry>;
  cacheType: string;
}

export default function DetailedCacheViewer({ cacheContents, cacheType }: DetailedCacheViewerProps) {
  const [expandedKeys, setExpandedKeys] = useState<Set<string>>(new Set());

  // Toggle expanded state for a key
  const toggleExpand = (key: string) => {
    const newExpandedKeys = new Set(expandedKeys);
    if (newExpandedKeys.has(key)) {
      newExpandedKeys.delete(key);
    } else {
      newExpandedKeys.add(key);
    }
    setExpandedKeys(newExpandedKeys);
  };

  // Format the value based on cache type
  const formatValue = (key: string, entry: CacheEntry) => {
    const value = entry.value;

    // If the value is null or undefined, return a placeholder
    if (value === null || value === undefined) {
      return <span className="text-gray-400">null</span>;
    }

    // For completions cache, try to extract the question and answer
    if (cacheType === "completions" && typeof value === "object") {
      try {
        // First, check for our enhanced cache metadata
        if (value._cacheMetadata) {
          const metadata = value._cacheMetadata;
          return (
            <div className="space-y-4">
              {/* Display model information */}
              <div className="bg-gray-100 p-2 rounded text-sm text-gray-700">
                <span className="font-medium">Model:</span> {metadata.model || "Unknown"}
              </div>

              {/* Display user question */}
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="font-semibold text-blue-800 mb-1">User Question:</div>
                <div className="whitespace-pre-wrap">{metadata.userQuestion || "No question found"}</div>
              </div>

              {/* Display assistant answer */}
              <div className="bg-green-50 p-3 rounded-lg">
                <div className="font-semibold text-green-800 mb-1">Assistant Answer:</div>
                <div className="whitespace-pre-wrap">{metadata.assistantAnswer || "No answer found"}</div>
              </div>

              {/* Display timestamp */}
              <div className="text-xs text-gray-500 mt-2">
                Original cache key: {metadata.originalCacheKey?.substring(0, 50)}...
              </div>
            </div>
          );
        }

        // Fall back to the old method of extracting messages
        const messages = value.messages || [];
        const userMessages = messages.filter((m: any) => m.role === "user");
        const assistantMessages = messages.filter((m: any) => m.role === "assistant");

        // If we have both user and assistant messages, format them nicely
        if (userMessages.length > 0 && assistantMessages.length > 0) {
          return (
            <div className="space-y-4">
              {userMessages.map((msg: any, i: number) => (
                <div key={`user-${i}`} className="bg-blue-50 p-3 rounded-lg">
                  <div className="font-semibold text-blue-800 mb-1">User:</div>
                  <div className="whitespace-pre-wrap">{msg.content}</div>
                </div>
              ))}
              {assistantMessages.map((msg: any, i: number) => (
                <div key={`assistant-${i}`} className="bg-green-50 p-3 rounded-lg">
                  <div className="font-semibold text-green-800 mb-1">Assistant:</div>
                  <div className="whitespace-pre-wrap">{msg.content}</div>
                </div>
              ))}
            </div>
          );
        }

        // Try to extract from choices if available
        if (value.choices && value.choices.length > 0) {
          const choice = value.choices[0];
          if (choice.message && choice.message.content) {
            return (
              <div className="space-y-4">
                <div className="bg-green-50 p-3 rounded-lg">
                  <div className="font-semibold text-green-800 mb-1">Response:</div>
                  <div className="whitespace-pre-wrap">{choice.message.content}</div>
                </div>
              </div>
            );
          }
        }
      } catch (e) {
        console.error("Error formatting completions cache:", e);
      }
    }

    // For guardrail cache, try to extract the prompt
    if (cacheType === "guardrail" && typeof value === "object") {
      try {
        // Extract prompt if it exists
        const prompt = value.prompt || value.input || value.text;
        if (prompt) {
          return (
            <div className="bg-yellow-50 p-3 rounded-lg">
              <div className="font-semibold text-yellow-800 mb-1">Prompt:</div>
              <div className="whitespace-pre-wrap">{prompt}</div>
            </div>
          );
        }
      } catch (e) {
        console.error("Error formatting guardrail cache:", e);
      }
    }

    // For session cache, try to extract session info
    if (cacheType === "session" && typeof value === "object") {
      try {
        return (
          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="font-semibold text-purple-800 mb-1">Session Info:</div>
            <pre className="text-xs overflow-auto">{JSON.stringify(value, null, 2)}</pre>
          </div>
        );
      } catch (e) {
        console.error("Error formatting session cache:", e);
      }
    }

    // Default: just stringify the value
    return (
      <pre className="text-xs overflow-auto max-h-[300px] bg-gray-50 p-2 rounded">
        {typeof value === "object"
          ? JSON.stringify(value, null, 2)
          : String(value)}
      </pre>
    );
  };

  // If there are no cache entries, show a message
  if (Object.keys(cacheContents).length === 0) {
    return (
      <div className="p-4 bg-gray-50 rounded text-center text-gray-500">
        No cache entries found.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="font-medium text-lg">Cache Entries ({Object.keys(cacheContents).length})</h3>

      {Object.entries(cacheContents).map(([key, entry]) => (
        <div key={key} className="border rounded-lg overflow-hidden">
          <div
            className="flex justify-between items-center p-3 bg-gray-100 cursor-pointer"
            onClick={() => toggleExpand(key)}
          >
            <div className="font-medium truncate max-w-[70%]" title={key}>
              {key}
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">{entry.formattedDate}</span>
              <button className="text-blue-600 hover:text-blue-800">
                {expandedKeys.has(key) ? "Collapse" : "Expand"}
              </button>
            </div>
          </div>

          {expandedKeys.has(key) && (
            <div className="p-4 bg-white">
              {formatValue(key, entry)}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
