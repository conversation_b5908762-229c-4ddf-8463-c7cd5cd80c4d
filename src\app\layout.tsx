import type { Metadata } from "next";
import "./globals.css";
import "./lib/envSetup";
// Initialize cache manager
import "./lib/cacheManager";

export const metadata: Metadata = {
  title: "Realtime API Agents",
  description: "A demo app from OpenAI.",
  // Explicitly set language to English
  alternates: {
    languages: {
      'en': '/',
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <meta httpEquiv="Content-Language" content="en" />
      </head>
      <body suppressHydrationWarning className="antialiased">{children}</body>
    </html>
  );
}
