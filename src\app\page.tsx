import React, { Suspense } from "react";
import { TranscriptProvider } from "@/app/contexts/TranscriptContext";
import { MultiTranscriptProvider } from "@/app/contexts/MultiTranscriptContext";
import { EventProvider } from "@/app/contexts/EventContext";
import App from "./App";

export default function Page() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TranscriptProvider>
        <MultiTranscriptProvider>
          <EventProvider>
            <App />
          </EventProvider>
        </MultiTranscriptProvider>
      </TranscriptProvider>
    </Suspense>
  );
}
