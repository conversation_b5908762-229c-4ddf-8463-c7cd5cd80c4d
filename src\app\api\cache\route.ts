import { NextResponse } from "next/server";
import {
  persistentGuardrailCache,
  persistentSessionCache,
  persistentCompletionsCache
} from "@/app/lib/persistentCache";

/**
 * GET endpoint to retrieve cache statistics
 *
 * Query parameters:
 * - action: Optional action to perform:
 *   - save: Force save all caches to disk
 *   - reset-save-attempts: Reset the save attempts counters to allow saving again
 */
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const action = searchParams.get("action");

    // Handle save action
    if (action === "save") {
      console.log("Manual cache save requested");

      const guardrailSaved = persistentGuardrailCache.saveToDisk(true);
      const sessionSaved = persistentSessionCache.saveToDisk(true);
      const completionsSaved = persistentCompletionsCache.saveToDisk(true);

      return NextResponse.json({
        message: "Manual cache save completed",
        results: {
          guardrail: guardrailSaved ? "success" : "failed",
          session: sessionSaved ? "success" : "failed",
          completions: completionsSaved ? "success" : "failed"
        }
      });
    }

    // Handle reset-save-attempts action
    if (action === "reset-save-attempts") {
      console.log("Resetting cache save attempts counters");

      persistentGuardrailCache.resetSaveAttempts();
      persistentSessionCache.resetSaveAttempts();
      persistentCompletionsCache.resetSaveAttempts();

      return NextResponse.json({
        message: "Cache save attempts counters reset successfully"
      });
    }

    // Default: return cache stats
    const stats = {
      guardrailCache: {
        size: persistentGuardrailCache.size()
      },
      sessionCache: {
        size: persistentSessionCache.size()
      },
      completionsCache: {
        size: persistentCompletionsCache.size()
      }
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error handling cache request:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE endpoint to clear cache
 *
 * Query parameters:
 * - type: The type of cache to clear (guardrail, session, completions, all)
 */
export async function DELETE(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const cacheType = searchParams.get("type");

    if (!cacheType) {
      return NextResponse.json(
        { error: "Cache type is required" },
        { status: 400 }
      );
    }

    let message = "";

    switch (cacheType) {
      case "guardrail":
        persistentGuardrailCache.clear();
        // Force save after clearing
        persistentGuardrailCache.saveToDisk(true);
        message = "Guardrail cache cleared";
        break;

      case "session":
        persistentSessionCache.clear();
        // Force save after clearing
        persistentSessionCache.saveToDisk(true);
        message = "Session cache cleared";
        break;

      case "completions":
        persistentCompletionsCache.clear();
        // Force save after clearing
        persistentCompletionsCache.saveToDisk(true);
        message = "Completions cache cleared";
        break;

      case "all":
        persistentGuardrailCache.clear();
        persistentSessionCache.clear();
        persistentCompletionsCache.clear();

        // Force save all after clearing
        persistentGuardrailCache.saveToDisk(true);
        persistentSessionCache.saveToDisk(true);
        persistentCompletionsCache.saveToDisk(true);

        message = "All caches cleared";
        break;

      default:
        return NextResponse.json(
          { error: "Invalid cache type. Use 'guardrail', 'session', 'completions', or 'all'." },
          { status: 400 }
        );
    }

    return NextResponse.json({ message });
  } catch (error) {
    console.error("Error clearing cache:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
