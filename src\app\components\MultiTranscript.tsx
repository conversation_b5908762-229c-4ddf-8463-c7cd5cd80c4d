"use client";

import React from "react";
import dynamic from "next/dynamic";

// Import the client-side only component with dynamic import
const ClientMultiTranscript = dynamic(() => import("./ClientMultiTranscript"), {
  ssr: false,
  loading: () => (
    <div className="flex flex-1 h-full items-center justify-center">
      <div className="text-gray-500">Loading transcript...</div>
    </div>
  ),
});

export interface MultiTranscriptProps {
  userText: string;
  setUserText: (val: string) => void;
  onSendMessage: () => void;
  canSend: boolean;
  downloadRecording: () => void;
  onClearTranscript?: () => void;
}

function MultiTranscript(props: MultiTranscriptProps) {
  return <ClientMultiTranscript {...props} />;
}

export default MultiTranscript;
