"use client";

import React, { useState, useCallback, useEffect } from "react";

interface ResizableDividerProps {
  onResize: (newPosition: number) => void;
  orientation?: "horizontal" | "vertical";
  initialPosition?: number;
  minSize?: number;
  maxSize?: number;
}

const ClientResizableDivider: React.FC<ResizableDividerProps> = ({
  onResize,
  orientation = "vertical",
  initialPosition = 50,
  minSize = 20,
  maxSize = 80,
}) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging) return;

      const containerElement = document.querySelector(".flex.flex-1.gap-2.px-2.overflow-hidden.relative");
      if (!containerElement) return;

      const containerRect = containerElement.getBoundingClientRect();
      
      let newPosition;
      if (orientation === "vertical") {
        // Calculate position as percentage of container width
        newPosition = ((e.clientX - containerRect.left) / containerRect.width) * 100;
      } else {
        // Calculate position as percentage of container height
        newPosition = ((e.clientY - containerRect.top) / containerRect.height) * 100;
      }

      // Clamp the position between minSize and maxSize
      newPosition = Math.max(minSize, Math.min(maxSize, newPosition));
      
      onResize(newPosition);
    },
    [isDragging, onResize, orientation, minSize, maxSize]
  );

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    } else {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div
      className={`
        ${orientation === "vertical" ? "w-1 cursor-col-resize" : "h-1 cursor-row-resize"} 
        bg-gray-300 hover:bg-blue-500 transition-colors
        ${isDragging ? "bg-blue-500" : ""}
        flex-shrink-0 z-10
      `}
      onMouseDown={handleMouseDown}
    />
  );
};

export default ClientResizableDivider;
