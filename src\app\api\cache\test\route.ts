import { NextResponse } from "next/server";
import {
  persistentGuardrailCache,
  persistentSessionCache,
  persistentCompletionsCache
} from "@/app/lib/persistentCache";

/**
 * GET endpoint to test if the cache API is working
 */
export async function GET() {
  try {
    // Add some test data to the caches
    persistentGuardrailCache.set("test-key-1", { value: "test value 1" });
    persistentSessionCache.set("test-key-2", { value: "test value 2" });

    // Add realistic completions cache entries with questions and answers
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");

    // Test entry 1: Java programming question
    persistentCompletionsCache.set(`Q_${timestamp}_What_is_polymorphism_in_Java`, {
      choices: [
        {
          message: {
            content: "Polymorphism in Java is a core object-oriented programming concept that allows objects of different classes to be treated as objects of a common superclass. It enables a single interface to represent different underlying forms (data types). In Java, polymorphism is primarily achieved through method overriding and method overloading."
          }
        }
      ],
      _cacheMetadata: {
        originalCacheKey: "test-original-key-1",
        userQuestion: "What is polymorphism in Java?",
        assistant<PERSON><PERSON>wer: "Polymorphism in Java is a core object-oriented programming concept that allows objects of different classes to be treated as objects of a common superclass. It enables a single interface to represent different underlying forms (data types). In Java, polymorphism is primarily achieved through method overriding and method overloading.",
        model: "gpt-4o-mini",
        timestamp: Date.now()
      }
    });

    // Test entry 2: Python programming question
    persistentCompletionsCache.set(`Q_${timestamp}_How_to_use_list_comprehension`, {
      choices: [
        {
          message: {
            content: "List comprehension in Python is a concise way to create lists. The basic syntax is: [expression for item in iterable if condition]. For example, to create a list of squares: squares = [x**2 for x in range(10)]. This creates [0, 1, 4, 9, 16, 25, 36, 49, 64, 81]."
          }
        }
      ],
      _cacheMetadata: {
        originalCacheKey: "test-original-key-2",
        userQuestion: "How do I use list comprehension in Python?",
        assistantAnswer: "List comprehension in Python is a concise way to create lists. The basic syntax is: [expression for item in iterable if condition]. For example, to create a list of squares: squares = [x**2 for x in range(10)]. This creates [0, 1, 4, 9, 16, 25, 36, 49, 64, 81].",
        model: "gpt-3.5-turbo",
        timestamp: Date.now() - 60000 // 1 minute ago
      }
    });

    // Get the cache entries
    const guardrailEntries = persistentGuardrailCache.getCacheEntries();
    const sessionEntries = persistentSessionCache.getCacheEntries();
    const completionsEntries = persistentCompletionsCache.getCacheEntries();

    return NextResponse.json({
      message: "Cache test successful",
      guardrailEntries: guardrailEntries.length,
      sessionEntries: sessionEntries.length,
      completionsEntries: completionsEntries.length,
      guardrailTtl: persistentGuardrailCache.getTtl(),
      sessionTtl: persistentSessionCache.getTtl(),
      completionsTtl: persistentCompletionsCache.getTtl(),
    });
  } catch (error) {
    console.error("Error testing cache:", error);
    return NextResponse.json(
      { error: "Internal Server Error", details: String(error) },
      { status: 500 }
    );
  }
}
