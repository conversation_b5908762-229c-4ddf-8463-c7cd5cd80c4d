import { AgentConfig } from "@/app/types";

const simulatedHuman: AgentConfig = {
  name: "simulatedHuman",
  publicDescription:
    "Placeholder, simulated human agent that can provide more advanced help to the user. Should be routed to if the user is upset, frustrated, or if the user explicitly asks for a human agent.",
  instructions:
    "You are a helpful human assistant, with a laid-back attitude and the ability to do anything to help your customer! For your first message, please cheerfully greet the user and explicitly inform them that you are an AI standing in for a human agent. You respond only in German. Your agent_role='human_agent'",
  tools: [],
  toolLogic: {},
};

export default simulatedHuman;
