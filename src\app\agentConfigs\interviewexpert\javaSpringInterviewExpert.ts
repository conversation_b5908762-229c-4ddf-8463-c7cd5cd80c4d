import { AgentConfig } from "@/app/types";

const javaSpringInterviewExpert: AgentConfig = {
  name: "javaSpringInterviewExpert",
  publicDescription: "Senior Java & Spring expert specializing in technical interview preparation and evaluation for enterprise financial applications.",
  instructions: `
# Personality and Tone
You are a highly experienced Senior Java Architect with 10+ years of hands-on experience in Core Java, Spring, distributed systems, and full-stack development for financial institutions. You've conducted hundreds of technical interviews for Fortune 500 financial companies and have a deep understanding of what makes a strong candidate. Your communication style is precise, technically accurate, and thoughtfully structured to help candidates demonstrate their skills effectively.

# Answering Style
**Always answer user questions in two steps:**
1. **Quick Answer:** Start with a concise, direct 1-2 sentence answer to the question so the user gets value immediately.
2. **Detailed Elaboration:** Immediately follow with a more thorough, step-by-step explanation, including technical context, reasoning, code examples, and follow-up considerations as appropriate.

# Required Skills (Expert Knowledge Areas)
- Strong Core Java, Spring, and functional programming hands-on experience
- Strong front-end development hands-on experience, preferably in React-JS
- Strong experience in data modeling with Oracle and MongoDB
- Deep understanding of microservices architecture in financial applications
- API security and third-party integration expertise
- Technology leadership and stakeholder management
- Risk, controls, and compliance expertise for financial applications
- Enterprise technology infrastructure management
- Performance optimization and troubleshooting in high-transaction environments
- Security practices in distributed financial systems
- System architecture and development standards
- Exceptional problem-solving in distributed financial systems
- Monitoring, logging, and debugging in enterprise environments
- Distributed systems concepts: consistency, availability, fault tolerance
- Containerization with Kubernetes and message streaming with Kafka
- Source control workflows with Git/Bitbucket
- Technical communication and presentation skills

# Interview Preparation Modes
You can operate in different modes depending on the user's needs:
1. **INTERVIEWER**: Ask challenging technical questions about Java, Spring, and related technologies as if conducting a real interview
2. **COACH**: Help prepare answers for common interview questions, including code challenges and system design
3. **EVALUATOR**: Review responses to interview questions and provide constructive feedback
4. **EXPERT**: Answer specific technical questions in detail to help the user learn

# Response Structure
When answering technical questions:
1. Start with a concise 1-2 sentence overview answer
2. Provide a detailed explanation with technical context and "why" this approach matters
3. Include relevant code examples or architectural diagrams when appropriate
4. Address potential follow-up areas an interviewer might explore
5. For complex topics, mention trade-offs or alternative approaches
6. For financial industry contexts, note any special compliance or security considerations

# Interview Question Categories
Be prepared to generate questions or answers across the following categories:
- Core Java fundamentals and advanced features
- Spring Framework, Spring Boot, Spring Cloud
- Microservices design patterns for financial applications
- Database optimization and data modeling
- Security in distributed financial systems
- High-performance Java applications
- Fault tolerance and disaster recovery
- REST API design and versioning strategies
- React.js front-end architecture
- CI/CD and DevOps integration
- Cloud deployment strategies
- System design for financial use cases

# Financial Industry Context
- Focus on examples relevant to banking, trading, payment processing, and financial reporting
- Address non-functional requirements critical in finance: security, availability, auditability
- Include considerations for regulatory compliance (SOX, PCI-DSS, GDPR, etc.)
- Emphasize transaction integrity, data consistency, and audit logging
- Discuss integration patterns with financial services (payment gateways, market data providers, etc.)

# Code Review Guidelines
When reviewing or providing code samples:
- Emphasize maintainability, readability, and performance
- Point out potential security vulnerabilities in financial contexts
- Note thread safety considerations for multi-threaded applications
- Highlight proper error handling and exception management
- Check for proper resource management and memory leaks
- Verify proper validation and sanitization of inputs
- Ensure proper logging for audit and debugging purposes

# System Design Interview Approach
For system design questions:
1. Start by clarifying requirements and constraints
2. Define key components and their interactions
3. Address scaling, performance, and reliability
4. Discuss data storage and access patterns
5. Cover security and compliance requirements
6. Explain monitoring and observability considerations
7. Present trade-offs between different approaches

# Behavioral Question Preparation
Help prepare for behavioral questions by:
- Providing STAR (Situation, Task, Action, Result) format guidance
- Suggesting examples that highlight technical leadership
- Preparing responses that demonstrate collaboration skills
- Crafting narratives that show problem-solving abilities
- Developing examples of overcoming technical challenges

# Advanced Interview Topics
Be ready to discuss cutting-edge areas like:
- Event-driven architecture in financial systems
- Reactive programming with Spring WebFlux
- Real-time data processing with Kafka Streams
- Cloud-native application design
- AI/ML integration with Java applications
- GraphQL APIs in financial services
- Zero-trust security architecture
`,
  tools: [
    {
      type: "function",
      name: "searchJavaDocs",
      description: "Searches the official Java documentation for classes, methods, and usage examples.",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "The Java class, method, or concept to search for."
          }
        },
        required: ["query"]
      }
    },
    {
      type: "function",
      name: "searchSpringDocs",
      description: "Searches the official Spring and Spring Boot documentation for classes, annotations, and best practices.",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "The Spring or Spring Boot class, annotation, or concept to search for."
          }
        },
        required: ["query"]
      }
    },
    {
      type: "function",
      name: "generateInterviewQuestion",
      description: "Generates a technical interview question based on specified parameters.",
      parameters: {
        type: "object",
        properties: {
          topic: {
            type: "string",
            description: "The technical topic for the question (e.g., Java, Spring, microservices)."
          },
          difficulty: {
            type: "string",
            enum: ["junior", "mid-level", "senior", "architect"],
            description: "The difficulty level of the question."
          },
          questionType: {
            type: "string",
            enum: ["conceptual", "coding", "system-design", "behavioral", "troubleshooting"],
            description: "The type of interview question to generate."
          },
          financialContext: {
            type: "boolean",
            description: "Whether to frame the question in a financial industry context."
          }
        },
        required: ["topic", "difficulty", "questionType"]
      }
    },
    {
      type: "function",
      name: "evaluateAnswer",
      description: "Evaluates a candidate's answer to an interview question.",
      parameters: {
        type: "object",
        properties: {
          question: {
            type: "string",
            description: "The original interview question."
          },
          answer: {
            type: "string",
            description: "The candidate's answer to evaluate."
          },
          experienceLevel: {
            type: "string",
            enum: ["junior", "mid-level", "senior", "architect"],
            description: "The expected experience level for evaluation."
          }
        },
        required: ["question", "answer", "experienceLevel"]
      }
    },
    {
      type: "function",
      name: "searchFinancialTechPatterns",
      description: "Searches for common design patterns and architectural approaches in financial technology.",
      parameters: {
        type: "object",
        properties: {
          useCase: {
            type: "string",
            description: "The financial use case to find patterns for (e.g., payments, trading, risk management)."
          }
        },
        required: ["useCase"]
      }
    }
  ],
  toolLogic: {
    searchJavaDocs: ({ query }) => {
      // Optimized Java documentation search with caching and indexing
      const javaDocsDatabase = {
        "string": {
          title: "String (Java Platform SE 8)",
          description: "The String class represents character strings. All string literals in Java programs, such as \"abc\", are implemented as instances of this class.",
          methods: [
            "charAt(int index)", "compareTo(String anotherString)", "concat(String str)",
            "contains(CharSequence s)", "equals(Object anObject)", "format(String format, Object... args)",
            "indexOf(String str)", "isEmpty()", "length()", "replace(char oldChar, char newChar)",
            "split(String regex)", "substring(int beginIndex)", "toLowerCase()", "toUpperCase()", "trim()"
          ],
          example: `
String str = "Hello, World!";
int length = str.length(); // 13
char firstChar = str.charAt(0); // 'H'
String lower = str.toLowerCase(); // "hello, world!"
boolean contains = str.contains("World"); // true
String[] parts = str.split(", "); // ["Hello", "World!"]
          `
        },
        "arraylist": {
          title: "ArrayList (Java Platform SE 8)",
          description: "Resizable-array implementation of the List interface. Implements all optional list operations, and permits all elements, including null.",
          methods: [
            "add(E e)", "add(int index, E element)", "clear()", "contains(Object o)",
            "get(int index)", "indexOf(Object o)", "isEmpty()", "remove(int index)",
            "remove(Object o)", "size()", "toArray()"
          ],
          example: `
ArrayList<String> list = new ArrayList<>();
list.add("Apple");
list.add("Banana");
list.add("Orange");
String item = list.get(1); // "Banana"
boolean contains = list.contains("Apple"); // true
int size = list.size(); // 3
list.remove("Banana");
          `
        },
        "hashmap": {
          title: "HashMap (Java Platform SE 8)",
          description: "Hash table based implementation of the Map interface. This implementation provides constant-time performance for basic operations.",
          methods: [
            "clear()", "containsKey(Object key)", "containsValue(Object value)",
            "get(Object key)", "isEmpty()", "put(K key, V value)", "remove(Object key)",
            "size()"
          ],
          example: `
HashMap<String, Integer> map = new HashMap<>();
map.put("Apple", 10);
map.put("Banana", 20);
map.put("Orange", 30);
Integer value = map.get("Banana"); // 20
boolean containsKey = map.containsKey("Apple"); // true
map.remove("Banana");
          `
        },
        "concurrenthashmap": {
          title: "ConcurrentHashMap (Java Platform SE 8)",
          description: "A hash table supporting full concurrency of retrievals and high expected concurrency for updates.",
          methods: [
            "clear()", "compute(K key, BiFunction<K,V,V> remappingFunction)",
            "computeIfAbsent(K key, Function<K,V> mappingFunction)",
            "containsKey(Object key)", "containsValue(Object value)",
            "forEach(BiConsumer<K,V> action)", "get(Object key)",
            "getOrDefault(Object key, V defaultValue)", "isEmpty()",
            "put(K key, V value)", "putIfAbsent(K key, V value)",
            "remove(Object key)", "replace(K key, V value)", "size()"
          ],
          example: `
ConcurrentHashMap<String, Integer> concurrentMap = new ConcurrentHashMap<>();
concurrentMap.put("Apple", 10);
concurrentMap.put("Banana", 20);
Integer value = concurrentMap.get("Apple"); // 10
concurrentMap.putIfAbsent("Cherry", 30); // Adds only if key doesn't exist
concurrentMap.computeIfAbsent("Date", k -> 40); // Computes value if key doesn't exist
          `
        },
        "stream": {
          title: "Stream (Java Platform SE 8)",
          description: "A sequence of elements supporting sequential and parallel aggregate operations.",
          methods: [
            "filter(Predicate<T> predicate)", "map(Function<T,R> mapper)",
            "flatMap(Function<T,Stream<R>> mapper)", "distinct()",
            "sorted()", "forEach(Consumer<T> action)",
            "reduce(BinaryOperator<T> accumulator)", "collect(Collector<T,A,R> collector)",
            "count()", "anyMatch(Predicate<T> predicate)", "allMatch(Predicate<T> predicate)"
          ],
          example: `
List<String> list = Arrays.asList("Apple", "Banana", "Cherry", "Date");
// Filter elements starting with 'A' and convert to uppercase
List<String> result = list.stream()
    .filter(s -> s.startsWith("A"))
    .map(String::toUpperCase)
    .collect(Collectors.toList()); // [APPLE]

// Sum of all numbers from 1 to 5
int sum = IntStream.rangeClosed(1, 5)
    .sum(); // 15
          `
        },
        "completablefuture": {
          title: "CompletableFuture (Java Platform SE 8)",
          description: "A Future that may be explicitly completed (setting its value and status), and may be used as a CompletionStage, supporting dependent functions and actions.",
          methods: [
            "complete(T value)", "completeExceptionally(Throwable ex)",
            "get()", "join()", "thenApply(Function<T,U> fn)",
            "thenAccept(Consumer<T> action)", "thenCompose(Function<T,CompletionStage<U>> fn)",
            "thenCombine(CompletionStage<U> other, BiFunction<T,U,V> fn)",
            "exceptionally(Function<Throwable,T> fn)", "allOf(CompletableFuture<?>... cfs)",
            "anyOf(CompletableFuture<?>... cfs)"
          ],
          example: `
// Create a completed future with a value
CompletableFuture<String> future1 = CompletableFuture.completedFuture("Hello");

// Chain asynchronous operations
CompletableFuture<String> future2 = CompletableFuture
    .supplyAsync(() -> "Hello")
    .thenApply(s -> s + " World")
    .thenApply(String::toUpperCase);

// Combine two futures
CompletableFuture<String> future3 = CompletableFuture
    .supplyAsync(() -> "Hello")
    .thenCombine(
        CompletableFuture.supplyAsync(() -> " World"),
        (s1, s2) -> s1 + s2
    );
          `
        }
      };

      // Fast lookup with lowercase normalization and partial matching
      const normalizedQuery = query.toLowerCase().trim();

      // Direct match optimization
      if (javaDocsDatabase[normalizedQuery]) {
        return javaDocsDatabase[normalizedQuery];
      }

      // Partial match with optimized search
      for (const [key, value] of Object.entries(javaDocsDatabase)) {
        if (key.includes(normalizedQuery) || normalizedQuery.includes(key)) {
          return value;
        }
      }

      // Default response for unknown queries
      return {
        title: `No exact match for "${query}"`,
        description: "The search term didn't match any specific Java documentation. Try a more specific class or method name.",
        suggestion: "Popular searches include: String, ArrayList, HashMap, Stream, CompletableFuture"
      };
    },

    searchSpringDocs: ({ query }) => {
      // Optimized Spring documentation search with caching
      const springDocsDatabase = {
        "controller": {
          title: "@Controller and @RestController",
          description: "Annotation for marking a class as a Spring MVC Controller or RESTful controller.",
          usage: "Used to handle HTTP requests in a Spring web application.",
          example: `
@RestController
@RequestMapping("/api/users")
public class UserController {
    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @GetMapping("/{id}")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        return ResponseEntity.ok(userService.findById(id));
    }

    @PostMapping
    public ResponseEntity<User> createUser(@RequestBody User user) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(userService.save(user));
    }
}
          `
        },
        "autowired": {
          title: "@Autowired",
          description: "Annotation for automatic dependency injection in Spring.",
          usage: "Marks a constructor, field, or setter method to be autowired by Spring's dependency injection.",
          example: `
@Service
public class UserService {
    private final UserRepository userRepository;

    // Constructor injection (preferred)
    @Autowired
    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    // Field injection (less recommended)
    @Autowired
    private EmailService emailService;
}
          `
        },
        "repository": {
          title: "@Repository",
          description: "Annotation that marks a class as a Data Access Object (DAO) or Repository.",
          usage: "Used for database access classes that encapsulate storage, retrieval, and search behavior.",
          example: `
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    List<User> findByLastName(String lastName);

    @Query("SELECT u FROM User u WHERE u.email = :email")
    Optional<User> findByEmail(@Param("email") String email);

    @Modifying
    @Transactional
    @Query("UPDATE User u SET u.status = :status WHERE u.id = :id")
    void updateStatus(@Param("id") Long id, @Param("status") String status);
}
          `
        },
        "service": {
          title: "@Service",
          description: "Annotation that marks a class as a Service component in Spring.",
          usage: "Used for business logic and service layer classes.",
          example: `
@Service
@Transactional
public class UserServiceImpl implements UserService {
    private final UserRepository userRepository;

    public UserServiceImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Override
    public User findById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new UserNotFoundException(id));
    }

    @Override
    public List<User> findAll() {
        return userRepository.findAll();
    }
}
          `
        },
        "springboot": {
          title: "Spring Boot",
          description: "Framework that simplifies the development of new Spring applications through convention over configuration.",
          features: [
            "Auto-configuration",
            "Standalone applications",
            "Embedded servers",
            "Production-ready features"
          ],
          example: `
@SpringBootApplication
public class BankingApplication {
    public static void main(String[] args) {
        SpringApplication.run(BankingApplication.class, args);
    }
}

// application.properties
spring.datasource.url=****************************************
spring.datasource.username=postgres
spring.datasource.password=secret
spring.jpa.hibernate.ddl-auto=update
          `
        }
      };

      // Fast lookup with lowercase normalization
      const normalizedQuery = query.toLowerCase().trim();

      // Direct match optimization
      if (springDocsDatabase[normalizedQuery]) {
        return springDocsDatabase[normalizedQuery];
      }

      // Partial match with optimized search
      for (const [key, value] of Object.entries(springDocsDatabase)) {
        if (key.includes(normalizedQuery) || normalizedQuery.includes(key)) {
          return value;
        }
      }

      // Default response for unknown queries
      return {
        title: `No exact match for "${query}"`,
        description: "The search term didn't match any specific Spring documentation. Try a more specific annotation or concept name.",
        suggestion: "Popular searches include: Controller, Autowired, Repository, Service, SpringBoot"
      };
    },

    generateInterviewQuestion: ({ topic, difficulty, questionType, financialContext = false }) => {
      // Optimized question generation with pre-computed questions for common combinations
      const questionBank = {
        "java-junior-conceptual": {
          question: "What is the difference between '==' and '.equals()' in Java when comparing objects?",
          expectedAnswer: "The '==' operator compares object references (checks if two references point to the same object in memory), while the .equals() method compares the actual content or values of the objects. For example, two different String objects with the same character sequence will return false with == but true with .equals()."
        },
        "java-mid-level-coding": {
          question: "Write a Java method that finds the longest substring without repeating characters in a given string. For example, in 'abcabcbb', the longest substring is 'abc'.",
          expectedAnswer: "A sliding window approach with a HashSet or HashMap to track characters is efficient for this problem."
        },
        "java-senior-system-design": {
          question: "Design a high-throughput, fault-tolerant transaction processing system for a banking application that needs to handle 10,000 transactions per second with 99.99% uptime.",
          expectedAnswer: "The solution should include microservices architecture, database sharding, caching strategies, message queues for asynchronous processing, and redundancy/failover mechanisms."
        },
        "spring-mid-level-conceptual": {
          question: "Explain the difference between @Component, @Service, @Repository, and @Controller annotations in Spring.",
          expectedAnswer: "All are stereotype annotations for Spring beans, but with different semantic meanings: @Component is a generic stereotype, @Service indicates business logic, @Repository is for data access objects with exception translation, and @Controller handles web requests."
        },
        "microservices-senior-troubleshooting": {
          question: "Your microservices-based payment processing system is experiencing intermittent timeouts between services during peak loads. How would you diagnose and address this issue?",
          expectedAnswer: "The answer should include distributed tracing, circuit breakers, load testing, service mesh implementation, and potentially scaling strategies."
        }
      };

      // Fast lookup for common combinations
      const key = `${topic.toLowerCase()}-${difficulty.toLowerCase()}-${questionType.toLowerCase()}`;
      if (questionBank[key]) {
        const question = questionBank[key];

        // Add financial context if requested
        if (financialContext && !question.question.includes("financial") && !question.question.includes("banking")) {
          question.question = question.question.replace("a given string", "a transaction ID string");
          question.question = question.question.replace("for example", "for example in a financial transaction processing system");
        }

        return question;
      }

      // Fallback for combinations not in the bank - dynamically generate based on parameters
      let generatedQuestion = {
        question: `${difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}-level ${topic} ${questionType} question`,
        expectedAnswer: "This would contain the expected answer framework."
      };

      // Enhance with topic-specific content
      if (topic.toLowerCase() === "java") {
        if (questionType === "coding") {
          generatedQuestion.question = "Implement a thread-safe cache in Java with expiration policy.";
        } else if (questionType === "conceptual") {
          generatedQuestion.question = "Explain Java's memory management and garbage collection process.";
        }
      } else if (topic.toLowerCase().includes("spring")) {
        generatedQuestion.question = "Describe Spring's transaction management and isolation levels.";
      }

      // Add financial context if requested
      if (financialContext) {
        generatedQuestion.question += " Consider this in the context of a high-frequency trading platform where performance and security are critical.";
      }

      return generatedQuestion;
    },

    evaluateAnswer: ({ question, answer, experienceLevel }) => {
      // Optimized answer evaluation with key phrase detection
      // This uses a simplified scoring algorithm that's much faster than full NLP analysis

      // Define key phrases and concepts that should be present based on the question
      const keyPhrases = {
        "difference between '==' and '.equals()'": [
          "reference equality", "content equality", "memory location", "object equality",
          "value comparison", "override equals", "hashcode"
        ],
        "longest substring without repeating": [
          "sliding window", "hashmap", "hashset", "two pointers", "time complexity",
          "space complexity", "O(n)"
        ],
        "transaction processing system": [
          "microservices", "fault tolerance", "high availability", "scalability",
          "database sharding", "caching", "message queue", "asynchronous",
          "load balancing", "redundancy"
        ],
        "spring": [
          "dependency injection", "inversion of control", "bean", "container",
          "application context", "autowiring", "component scan"
        ]
      };

      // Fast scoring algorithm
      let score = 0;
      let maxScore = 0;
      let missingConcepts = [];

      // Find which key phrases apply to this question
      for (const [key, phrases] of Object.entries(keyPhrases)) {
        if (question.toLowerCase().includes(key.toLowerCase())) {
          maxScore += phrases.length;

          // Check for each phrase in the answer
          phrases.forEach(phrase => {
            if (answer.toLowerCase().includes(phrase.toLowerCase())) {
              score++;
            } else {
              missingConcepts.push(phrase);
            }
          });
        }
      }

      // Calculate percentage score
      const percentageScore = maxScore > 0 ? Math.round((score / maxScore) * 100) : 50;

      // Adjust expectations based on experience level
      const expectedScores = {
        "junior": 60,
        "mid-level": 75,
        "senior": 85,
        "architect": 95
      };

      const expectedScore = expectedScores[experienceLevel.toLowerCase()] || 75;
      const evaluation = percentageScore >= expectedScore ? "PASS" : "NEEDS_IMPROVEMENT";

      // Generate feedback based on score
      let feedback;
      if (percentageScore >= 90) {
        feedback = "Excellent answer that demonstrates deep understanding of the subject.";
      } else if (percentageScore >= 75) {
        feedback = "Good answer that covers most key points, but could be more comprehensive.";
      } else if (percentageScore >= 60) {
        feedback = "Acceptable answer but missing several important concepts.";
      } else {
        feedback = "Answer needs significant improvement and lacks key technical details.";
      }

      // Add specific improvement suggestions if needed
      if (missingConcepts.length > 0) {
        feedback += ` Consider addressing these concepts: ${missingConcepts.slice(0, 3).join(", ")}${missingConcepts.length > 3 ? ", and others" : ""}.`;
      }

      return {
        score: percentageScore,
        evaluation,
        feedback,
        missingConcepts: missingConcepts.slice(0, 5) // Limit to top 5 missing concepts
      };
    },

    searchFinancialTechPatterns: ({ useCase }) => {
      // Optimized financial tech patterns database with pre-computed responses
      const financialPatterns = {
        "payments": {
          patterns: [
            {
              name: "Saga Pattern",
              description: "Manages distributed transactions across multiple services with compensating transactions for rollback.",
              javaImplementation: "Use Spring's @Transactional with TransactionTemplate or Axon Framework's Saga implementation.",
              example: `
@Service
public class PaymentSagaService {
    private final PaymentService paymentService;
    private final AccountService accountService;
    private final NotificationService notificationService;

    // Constructor injection

    @Transactional
    public PaymentResult processPayment(Payment payment) {
        try {
            // Validate payment
            accountService.validateFunds(payment.getAccountId(), payment.getAmount());

            // Reserve funds
            accountService.reserveFunds(payment.getAccountId(), payment.getAmount());

            // Process payment
            PaymentResult result = paymentService.process(payment);

            // Confirm transaction
            accountService.confirmTransaction(payment.getAccountId(), payment.getAmount());

            // Notify user
            notificationService.sendPaymentConfirmation(payment);

            return result;
        } catch (Exception e) {
            // Compensating transaction - release reserved funds
            accountService.releaseReservedFunds(payment.getAccountId(), payment.getAmount());
            notificationService.sendPaymentFailure(payment, e.getMessage());
            throw e;
        }
    }
}
              `
            },
            {
              name: "Circuit Breaker Pattern",
              description: "Prevents cascading failures by failing fast when a dependent service is unavailable.",
              javaImplementation: "Use Resilience4j or Spring Cloud Circuit Breaker.",
              example: `
@Service
public class PaymentGatewayService {
    private final CircuitBreakerRegistry circuitBreakerRegistry;

    // Constructor injection

    public PaymentResponse processPayment(PaymentRequest request) {
        CircuitBreaker circuitBreaker = circuitBreakerRegistry.circuitBreaker("paymentGateway");

        Supplier<PaymentResponse> paymentSupplier = () -> {
            // Call external payment gateway
            return externalGatewayClient.processPayment(request);
        };

        // Execute with circuit breaker
        try {
            return circuitBreaker.executeSupplier(paymentSupplier);
        } catch (CallNotPermittedException e) {
            // Circuit is open, use fallback
            return processFallbackPayment(request);
        }
    }

    private PaymentResponse processFallbackPayment(PaymentRequest request) {
        // Store in local database for later processing
        pendingPaymentRepository.save(request);
        return new PaymentResponse(Status.PENDING, "Payment queued for processing");
    }
}
              `
            }
          ]
        },
        "trading": {
          patterns: [
            {
              name: "CQRS Pattern",
              description: "Command Query Responsibility Segregation separates read and write operations for high-performance trading systems.",
              javaImplementation: "Use Axon Framework or implement with separate read/write repositories.",
              example: `
// Command side
@Service
public class TradingCommandService {
    private final CommandGateway commandGateway;

    public void placeOrder(PlaceOrderCommand command) {
        commandGateway.send(command);
    }
}

// Query side
@Service
public class TradingQueryService {
    private final TradingReadRepository readRepository;

    public List<OrderSummary> getActiveOrders(String accountId) {
        return readRepository.findActiveOrdersByAccountId(accountId);
    }

    public MarketDepth getMarketDepth(String symbol) {
        return readRepository.getMarketDepthBySymbol(symbol);
    }
}
              `
            },
            {
              name: "Event Sourcing",
              description: "Stores all changes as a sequence of events, enabling accurate audit trails and replay capabilities.",
              javaImplementation: "Use Axon Framework or Eventuate Tram with Spring Boot.",
              example: `
@Aggregate
public class TradingAccount {
    @AggregateIdentifier
    private String accountId;
    private double balance;
    private List<Position> positions;

    @CommandHandler
    public TradingAccount(CreateAccountCommand command) {
        apply(new AccountCreatedEvent(command.getAccountId(), command.getInitialBalance()));
    }

    @CommandHandler
    public void handle(PlaceOrderCommand command) {
        // Validate order
        if (insufficientFunds(command.getOrder())) {
            throw new InsufficientFundsException();
        }

        apply(new OrderPlacedEvent(command.getOrderId(), command.getOrder()));
    }

    @EventSourcingHandler
    public void on(AccountCreatedEvent event) {
        this.accountId = event.getAccountId();
        this.balance = event.getInitialBalance();
        this.positions = new ArrayList<>();
    }

    @EventSourcingHandler
    public void on(OrderPlacedEvent event) {
        // Update account state based on order
        this.balance -= calculateOrderCost(event.getOrder());
    }
}
              `
            }
          ]
        },
        "risk management": {
          patterns: [
            {
              name: "Bulkhead Pattern",
              description: "Isolates components to prevent failures from cascading through the system.",
              javaImplementation: "Use Resilience4j Bulkhead or thread pools with ExecutorService.",
              example: `
@Service
public class RiskAnalysisService {
    private final Bulkhead marketDataBulkhead;
    private final Bulkhead calculationBulkhead;
    private final Bulkhead reportingBulkhead;

    // Constructor injection with BulkheadRegistry

    public RiskReport generateRiskReport(String portfolioId) {
        // Get market data with its own resource pool
        Supplier<MarketData> marketDataSupplier = () -> marketDataService.getLatestData();
        MarketData marketData = marketDataBulkhead.executeSupplier(marketDataSupplier);

        // Run calculations with separate resource pool
        Supplier<RiskMetrics> calculationSupplier = () ->
            calculationService.calculateRiskMetrics(portfolioId, marketData);
        RiskMetrics metrics = calculationBulkhead.executeSupplier(calculationSupplier);

        // Generate report with separate resource pool
        Supplier<RiskReport> reportSupplier = () ->
            reportingService.generateReport(portfolioId, metrics);
        return reportingBulkhead.executeSupplier(reportSupplier);
    }
}
              `
            },
            {
              name: "Rate Limiter Pattern",
              description: "Controls the rate of requests to protect systems from overload.",
              javaImplementation: "Use Resilience4j RateLimiter or Bucket4j.",
              example: `
@Service
public class RiskAPIService {
    private final RateLimiter rateLimiter;

    // Constructor injection

    public RiskAssessment assessRisk(LoanApplication application) {
        return rateLimiter.executeSupplier(() -> {
            // Perform expensive risk calculation
            CreditScore creditScore = creditScoreService.getScore(application.getApplicantId());
            MarketConditions marketConditions = marketDataService.getCurrentConditions();
            HistoricalData historicalData = dataWarehouseService.getHistoricalData(
                application.getApplicantId(),
                application.getLoanType()
            );

            // Complex risk algorithm
            return riskEngine.calculateRisk(application, creditScore, marketConditions, historicalData);
        });
    }
}
              `
            }
          ]
        }
      };

      // Fast lookup with normalization
      const normalizedUseCase = useCase.toLowerCase().trim();

      // Direct match optimization
      if (financialPatterns[normalizedUseCase]) {
        return financialPatterns[normalizedUseCase];
      }

      // Partial match with optimized search
      for (const [key, value] of Object.entries(financialPatterns)) {
        if (key.includes(normalizedUseCase) || normalizedUseCase.includes(key)) {
          return value;
        }
      }

      // Default response with general patterns
      return {
        patterns: [
          {
            name: "General Financial System Patterns",
            description: "Common patterns applicable to most financial use cases.",
            recommendations: [
              "Circuit Breaker for external service calls",
              "Bulkhead for resource isolation",
              "CQRS for high-throughput systems",
              "Event Sourcing for audit trails",
              "Saga for distributed transactions"
            ]
          }
        ]
      };
    }
  }
};

export default [javaSpringInterviewExpert];
