# Persistent Cache Implementation

This directory contains the implementation of a persistent caching system that allows data to be preserved between application restarts.

## Components

### 1. LRUCache (cache.ts)

The base in-memory LRU (Least Recently Used) cache implementation. This provides:
- Memory-based caching with TTL (Time To Live)
- LRU eviction policy when the cache reaches capacity
- Basic cache operations (get, set, has, clear)

### 2. PersistentLRUCache (persistentCache.ts)

Extends the base LRU cache with persistence capabilities:
- Saves cache data to disk in JSON format
- Loads cache data from disk on initialization
- Automatically saves dirty cache data at configurable intervals
- Handles serialization and deserialization of cache data
- Preserves TTL and timestamp information

### 3. CacheManager (cacheManager.ts)

Manages all persistent caches and ensures they're properly saved:
- Registers shutdown handlers to save caches when the application exits
- Provides methods to manually save all caches
- Initializes as a singleton when imported

## Usage

### Using Persistent Caches

```typescript
// Import the persistent cache instances
import { 
  persistentGuardrailCache, 
  persistentSessionCache, 
  persistentCompletionsCache 
} from '@/app/lib/persistentCache';

// Use like a regular cache
persistentSessionCache.set('key', value);
const value = persistentSessionCache.get('key');

// Force immediate save to disk
persistentSessionCache.saveToDisk();
```

### Cache Configuration

The persistent caches are configured with the following parameters:

1. **persistentGuardrailCache**:
   - Max size: 500 items
   - TTL: 60 minutes
   - File: `.cache/guardrail.json`

2. **persistentSessionCache**:
   - Max size: 10 items
   - TTL: 5 minutes
   - File: `.cache/session.json`

3. **persistentCompletionsCache**:
   - Max size: 200 items
   - TTL: 30 minutes
   - File: `.cache/completions.json`

### Auto-Save Behavior

By default, caches are automatically saved to disk:
- Every 5 minutes if they have been modified
- When the application shuts down gracefully
- On SIGINT, SIGTERM, and uncaught exceptions

## Implementation Details

### Cache Storage

Cache files are stored in the `.cache` directory at the root of the project. Each cache has its own JSON file named after the cache ID.

### Serialization

Cache entries are serialized to JSON with the following structure:

```json
{
  "key1": {
    "value": "serialized value",
    "timestamp": 1234567890123
  },
  "key2": {
    "value": "another value",
    "timestamp": 1234567890456
  }
}
```

### Key Handling

Since JavaScript Map keys can be of any type but JSON object keys must be strings, the implementation:
1. Converts all keys to strings during serialization
2. Attempts to convert string keys back to their original type during deserialization
3. Handles common types like numbers, objects, and arrays

### Expiration

When loading from disk, expired entries are automatically filtered out based on their timestamp and the cache's TTL setting.
