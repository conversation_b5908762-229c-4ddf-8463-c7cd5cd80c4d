import { NextResponse } from "next/server";
import {
  persistentGuardrailCache,
  persistentSessionCache,
  persistentCompletionsCache
} from "@/app/lib/persistentCache";

/**
 * GET endpoint to retrieve the contents of a specific cache
 *
 * Query parameters:
 * - type: The type of cache to retrieve (guardrail, session, completions)
 */
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const cacheType = searchParams.get("type");

    if (!cacheType) {
      return NextResponse.json(
        { error: "Cache type is required" },
        { status: 400 }
      );
    }

    let cacheContents: Record<string, any> = {};

    // Get the appropriate cache based on the type
    switch (cacheType) {
      case "guardrail":
        cacheContents = getSerializedCacheContents(persistentGuardrailCache);
        break;

      case "session":
        cacheContents = getSerializedCacheContents(persistentSessionCache);
        break;

      case "completions":
        cacheContents = getSerializedCacheContents(persistentCompletionsCache);
        break;

      default:
        return NextResponse.json(
          { error: "Invalid cache type. Use 'guardrail', 'session', or 'completions'." },
          { status: 400 }
        );
    }

    return NextResponse.json(cacheContents);
  } catch (error) {
    console.error("Error getting cache contents:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE endpoint to remove a specific entry from a cache
 *
 * Query parameters:
 * - type: The type of cache (guardrail, session, completions)
 * - key: The key to delete (URL encoded)
 */
export async function DELETE(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const cacheType = searchParams.get("type");
    const key = searchParams.get("key");

    if (!cacheType) {
      return NextResponse.json(
        { error: "Cache type is required" },
        { status: 400 }
      );
    }

    if (!key) {
      return NextResponse.json(
        { error: "Cache key is required" },
        { status: 400 }
      );
    }

    let success = false;

    // Delete from the appropriate cache based on the type
    switch (cacheType) {
      case "guardrail":
        success = deleteFromCache(persistentGuardrailCache, key);
        break;

      case "session":
        success = deleteFromCache(persistentSessionCache, key);
        break;

      case "completions":
        success = deleteFromCache(persistentCompletionsCache, key);
        break;

      default:
        return NextResponse.json(
          { error: "Invalid cache type. Use 'guardrail', 'session', or 'completions'." },
          { status: 400 }
        );
    }

    if (success) {
      return NextResponse.json({ message: `Entry removed from ${cacheType} cache` });
    } else {
      return NextResponse.json(
        { error: "Entry not found in cache" },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error("Error deleting cache entry:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

/**
 * Helper function to get serialized cache contents
 */
function getSerializedCacheContents(cache: any): Record<string, any> {
  try {
    console.log("Getting cache entries...");

    // No longer adding test entries automatically to avoid unnecessary cache saves
    // Test entries should be added explicitly via the test endpoint

    // Access the internal cache using the protected method
    const entries = cache.getCacheEntries();
    console.log(`Found ${entries.length} entries in cache`);

    // Also try to access the internal cache directly
    // @ts-ignore - accessing private field
    const internalCache = cache.getInternalCache();
    console.log(`Internal cache size: ${internalCache.size}`);

    const contents: Record<string, any> = {};

    for (const [key, item] of entries) {
      // Convert key to string for use as object key
      const keyStr = String(key);
      console.log(`Processing cache entry with key: ${keyStr}`);

      // Format the timestamp as a readable date
      const date = new Date(item.timestamp);
      const formattedDate = date.toLocaleString();

      // Add entry to contents
      contents[keyStr] = {
        value: item.value,
        timestamp: item.timestamp,
        formattedDate,
        // Calculate time until expiration
        expiresIn: Math.max(0, Math.floor((item.timestamp + cache.getTtl() - Date.now()) / 1000))
      };
    }

    console.log(`Returning ${Object.keys(contents).length} serialized entries`);
    return contents;
  } catch (error) {
    console.error("Error serializing cache contents:", error);
    // Return a test object so we can see something
    return {
      "error-entry": {
        value: `Error: ${error}`,
        timestamp: Date.now(),
        formattedDate: new Date().toLocaleString(),
        expiresIn: 3600
      }
    };
  }
}

/**
 * Helper function to delete an entry from a cache
 */
function deleteFromCache(cache: any, keyStr: string): boolean {
  try {
    // Try to parse the key if it's a complex type
    const parsedKey = parseKey(keyStr);

    // Check if the key exists in the cache
    if (cache.has(parsedKey)) {
      // Get the internal cache Map
      const internalCache = cache.getInternalCache();

      // Delete the entry
      const result = internalCache.delete(parsedKey);

      // Mark the cache as dirty so it will be saved
      // @ts-ignore - accessing private field
      cache.isDirty = true;

      return result;
    }

    return false;
  } catch (error) {
    console.error("Error deleting from cache:", error);
    return false;
  }
}

/**
 * Parse a string key back to its original type
 * This is similar to the parseKey method in PersistentLRUCache
 */
function parseKey(keyStr: string): any {
  // Try to parse as JSON if it looks like an object or array
  if ((keyStr.startsWith('{') && keyStr.endsWith('}')) ||
      (keyStr.startsWith('[') && keyStr.endsWith(']'))) {
    try {
      return JSON.parse(keyStr);
    } catch {
      // Fall back to string if parsing fails
    }
  }

  // Try to convert to number if it looks like one
  if (/^-?\d+(\.\d+)?$/.test(keyStr)) {
    const num = Number(keyStr);
    if (!isNaN(num)) {
      return num;
    }
  }

  // Default to string
  return keyStr;
}
