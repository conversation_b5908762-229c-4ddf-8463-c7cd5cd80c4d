"use client";

import React from "react";
import dynamic from "next/dynamic";
import { TranscriptItem } from "@/app/types";

// Import the client-side only component with dynamic import
const ClientTranscript = dynamic(() => import("./ClientTranscript"), {
  ssr: false,
  loading: () => (
    <div className="flex flex-col h-full bg-white rounded-xl overflow-hidden items-center justify-center">
      <div className="text-gray-500">Loading transcript...</div>
    </div>
  ),
});

export interface TranscriptProps {
  userText: string;
  setUserText: (val: string) => void;
  onSendMessage: () => void;
  canSend: boolean;
  downloadRecording: () => void;
  // Optional props for multi-transcript mode
  transcriptItems?: TranscriptItem[];
  boxId?: string;
  isActiveBox?: boolean;
  parentScrollToBottom?: () => void;
  onClearTranscript?: () => void;
}

function Transcript(props: TranscriptProps) {
  return <ClientTranscript {...props} />;
}

export default Transcript;
