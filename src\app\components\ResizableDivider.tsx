"use client";

import React from "react";
import dynamic from "next/dynamic";

// Import the client-side only component with dynamic import
const ClientResizableDivider = dynamic(
  () => import("./ClientResizableDivider"),
  { ssr: false }
);

interface ResizableDividerProps {
  onResize: (newPosition: number) => void;
  orientation?: "horizontal" | "vertical";
  initialPosition?: number;
  minSize?: number;
  maxSize?: number;
}

const ResizableDivider: React.FC<ResizableDividerProps> = (props) => {
  return <ClientResizableDivider {...props} />;
};

export default ResizableDivider;
