import fs from 'fs';
import path from 'path';
import { LRUCache } from './cache';

/**
 * Directory where cache files will be stored
 */
const CACHE_DIR = path.join(process.cwd(), '.cache');

/**
 * Interval for auto-saving cache to disk (in milliseconds)
 * Default: 30 seconds (reduced from 5 minutes for more frequent saves)
 */
const DEFAULT_SAVE_INTERVAL = 30 * 1000;

/**
 * Check if caching is disabled via environment variable
 */
const CACHING_DISABLED = process.env.NEXT_PUBLIC_DISABLE_CACHING === 'true';

/**
 * A persistent LRU cache that saves to disk and can be restored between application restarts
 */
export class PersistentLRUCache<K, V> extends LRUCache<K, V> {
  private readonly cacheId: string;
  private readonly saveInterval: number;
  private saveTimer: NodeJS.Timeout | null = null;
  private isDirty: boolean = false;
  private saveAttempts: number = 0;
  private readonly maxSaveAttempts: number = 1; // Limit to 1 save attempt

  /**
   * Create a new persistent LRU cache
   * @param cacheId Unique identifier for this cache (used for filename)
   * @param maxSize Maximum number of items to store in memory
   * @param ttlMinutes Time to live in minutes
   * @param saveIntervalMs How often to save to disk (in milliseconds)
   */
  constructor(
    cacheId: string,
    maxSize: number = 100,
    ttlMinutes: number = 60,
    saveIntervalMs: number = DEFAULT_SAVE_INTERVAL
  ) {
    super(maxSize, ttlMinutes);
    this.cacheId = cacheId;
    this.saveInterval = saveIntervalMs;

    // Create cache directory if it doesn't exist
    this.ensureCacheDir();

    // Load existing cache from disk
    this.loadFromDisk();

    // Start auto-save timer
    this.startAutoSave();
  }

  /**
   * Override set method to mark cache as dirty
   */
  override set(key: K, value: V): void {
    // Skip caching if disabled
    if (CACHING_DISABLED) {
      // Suppress verbose logging
      return;
    }
    super.set(key, value);
    this.isDirty = true;
  }

  /**
   * Override get method to skip cache lookup if disabled
   */
  override get(key: K): V | undefined {
    // Skip cache lookup if disabled
    if (CACHING_DISABLED) {
      // Suppress verbose logging
      return undefined;
    }
    return super.get(key);
  }

  /**
   * Override clear method to mark cache as dirty
   */
  override clear(): void {
    super.clear();
    this.isDirty = true;
  }

  /**
   * Ensure the cache directory exists
   */
  private ensureCacheDir(): void {
    try {
      if (!fs.existsSync(CACHE_DIR)) {
        fs.mkdirSync(CACHE_DIR, { recursive: true });
      }
    } catch (error) {
      console.error('Failed to create cache directory:', error);
    }
  }

  /**
   * Get the full path to the cache file
   */
  private getCacheFilePath(): string {
    return path.join(CACHE_DIR, `${this.cacheId}.json`);
  }

  /**
   * Save the current cache to disk
   * @param force Force saving even if the cache is not dirty
   * @returns True if save was successful, false otherwise
   */
  saveToDisk(force: boolean = false): boolean {
    // Skip saving if caching is disabled
    if (CACHING_DISABLED) {
      // Suppress verbose logging
      return true;
    }

    // Reduced logging

    // Check if we've reached the maximum number of save attempts
    if (this.saveAttempts >= this.maxSaveAttempts) {
      // If force is true, we'll save anyway
      if (!force) {
        return true; // Skip saving if we've reached the maximum number of attempts and not forced
      }
    }

    if (!this.isDirty && !force) {
      return true; // Skip saving if nothing has changed and not forced
    }

    try {
      // Increment the save attempts counter
      this.saveAttempts++;

      // Ensure the cache directory exists
      this.ensureCacheDir();

      // Serialize the cache data
      const cacheData = this.serializeCache();
      const filePath = this.getCacheFilePath();

      // Write to a temporary file first, then rename to avoid corruption
      const tempFilePath = `${filePath}.tmp`;
      fs.writeFileSync(tempFilePath, JSON.stringify(cacheData));

      // On Windows, we need to unlink the existing file first
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      // Rename the temp file to the actual file
      fs.renameSync(tempFilePath, filePath);

      this.isDirty = false;
      return true;
    } catch (error) {
      console.error(`Failed to save cache ${this.cacheId} to disk:`, error);
      return false;
    }
  }

  /**
   * Load cache from disk
   * @returns True if load was successful, false otherwise
   */
  loadFromDisk(): boolean {
    // Skip loading if caching is disabled
    if (CACHING_DISABLED) {
      // Suppress verbose logging
      return true;
    }

    try {
      // Ensure the cache directory exists
      this.ensureCacheDir();

      const filePath = this.getCacheFilePath();

      if (!fs.existsSync(filePath)) {
        return false; // No cache file exists yet
      }

      const fileContent = fs.readFileSync(filePath, 'utf8');

      if (!fileContent || fileContent.trim() === '') {
        return false;
      }

      try {
        const cacheData = JSON.parse(fileContent);
        this.deserializeCache(cacheData);
        return true;
      } catch (parseError) {
        // If the file is corrupted, rename it and create a new one
        const backupPath = `${filePath}.corrupted`;

        try {
          if (fs.existsSync(backupPath)) {
            fs.unlinkSync(backupPath);
          }
          fs.renameSync(filePath, backupPath);
        } catch (renameError) {
          // Silently handle rename errors
        }

        return false;
      }
    } catch (error) {
      return false;
    }
  }

  /**
   * Serialize the cache for storage
   */
  private serializeCache(): any {
    const serialized: Record<string, any> = {};

    for (const [key, item] of this.getCacheEntries()) {
      // Only serialize if the key can be converted to a string
      const keyStr = String(key);
      serialized[keyStr] = {
        value: item.value,
        timestamp: item.timestamp
      };
    }

    return serialized;
  }

  /**
   * Deserialize and load cache data
   */
  private deserializeCache(data: Record<string, any>): void {
    const now = Date.now();
    const ttl = this.getTtl();

    for (const [keyStr, item] of Object.entries(data)) {
      // Skip expired items
      if (now - item.timestamp > ttl) {
        continue;
      }

      // Convert string key back to original type if possible
      const key = this.parseKey(keyStr);
      super.set(key, item.value);

      // Update the timestamp to the original one
      const cacheItem = this.getCacheItem(key);
      if (cacheItem) {
        cacheItem.timestamp = item.timestamp;
      }
    }
  }

  /**
   * Parse a string key back to its original type
   * This is a best-effort conversion and may not work for all key types
   */
  private parseKey(keyStr: string): K {
    // Try to parse as JSON if it looks like an object or array
    if ((keyStr.startsWith('{') && keyStr.endsWith('}')) ||
        (keyStr.startsWith('[') && keyStr.endsWith(']'))) {
      try {
        return JSON.parse(keyStr) as K;
      } catch {
        // Fall back to string if parsing fails
      }
    }

    // Try to convert to number if it looks like one
    if (/^-?\d+(\.\d+)?$/.test(keyStr)) {
      const num = Number(keyStr);
      if (!isNaN(num)) {
        return num as unknown as K;
      }
    }

    // Default to string
    return keyStr as unknown as K;
  }

  /**
   * Start the auto-save timer
   */
  private startAutoSave(): void {
    // Skip auto-save if caching is disabled
    if (CACHING_DISABLED) {
      // Suppress verbose logging
      return;
    }

    // Clear any existing timer
    if (this.saveTimer) {
      clearInterval(this.saveTimer);
    }

    // Set up new timer
    this.saveTimer = setInterval(() => {
      this.saveToDisk();
    }, this.saveInterval);

    // Ensure timer doesn't prevent Node from exiting
    if (this.saveTimer.unref) {
      this.saveTimer.unref();
    }
  }

  /**
   * Stop the auto-save timer
   */
  stopAutoSave(): void {
    if (this.saveTimer) {
      clearInterval(this.saveTimer);
      this.saveTimer = null;
    }
  }

  /**
   * Clean up resources when the cache is no longer needed
   */
  dispose(): void {
    this.stopAutoSave();
    this.saveToDisk(); // Final save
  }

  /**
   * Reset the save attempts counter
   * This allows saving again after the maximum number of attempts has been reached
   */
  resetSaveAttempts(): void {
    this.saveAttempts = 0;
    // Reduced logging
  }

  /**
   * Get access to the internal cache entries for serialization
   * Exposed as public for the cache viewer
   */
  getCacheEntries(): [K, { value: V; timestamp: number }][] {
    // Reduce verbose logging
    const internalCache = this.getInternalCache();

    // No longer adding test entries automatically to avoid unnecessary cache saves
    // Test entries should be added explicitly when needed

    const entries = Array.from(internalCache.entries());
    return entries;
  }

  /**
   * Get the TTL value in milliseconds
   * Exposed as public for the cache viewer
   */
  getTtl(): number {
    return this.getInternalTtl();
  }

  /**
   * Get a cache item directly
   * @private
   */
  private getCacheItem(key: K): { value: V; timestamp: number } | undefined {
    return this.getInternalCache().get(key);
  }

  /**
   * Access the internal cache Map (for serialization)
   * @private
   */
  protected getInternalCache(): Map<K, { value: V; timestamp: number }> {
    // @ts-ignore - accessing private field
    return this.cache;
  }

  /**
   * Access the internal TTL value (for expiration checking)
   * @private
   */
  protected getInternalTtl(): number {
    // @ts-ignore - accessing private field
    return this.ttl;
  }
}

// Create singleton instances for different persistent cache types
export const persistentGuardrailCache = new PersistentLRUCache<string, any>('guardrail', 500, 60);
export const persistentSessionCache = new PersistentLRUCache<string, any>('session', 10, 5);
export const persistentCompletionsCache = new PersistentLRUCache<string, any>('completions', 200, 30);

// Save all caches on process exit
if (typeof process !== 'undefined') {
  // Save caches on normal exit
  process.on('exit', () => {
    // Reduced logging
    persistentGuardrailCache.saveToDisk(true);
    persistentSessionCache.saveToDisk(true);
    persistentCompletionsCache.saveToDisk(true);
  });

  // Save caches on SIGINT (Ctrl+C)
  process.on('SIGINT', () => {
    // Reduced logging
    persistentGuardrailCache.saveToDisk(true);
    persistentSessionCache.saveToDisk(true);
    persistentCompletionsCache.saveToDisk(true);
    process.exit(0);
  });

  // Save caches on uncaught exceptions
  process.on('uncaughtException', (err) => {
    console.error('Uncaught exception:', err);
    // Reduced logging
    persistentGuardrailCache.saveToDisk(true);
    persistentSessionCache.saveToDisk(true);
    persistentCompletionsCache.saveToDisk(true);
    process.exit(1);
  });
}
