"use client";

import React, { useEffect, useRef, useState, useCallback } from "react";
import ReactMarkdown from "react-markdown";
import { TranscriptItem } from "@/app/types";
import Image from "next/image";
import { useTranscript } from "@/app/contexts/TranscriptContext";
import { DownloadIcon, ClipboardCopyIcon, ArrowDownIcon, ArrowUpIcon } from "@radix-ui/react-icons";
import { GuardrailChip } from "./GuardrailChip";
import ResizableDivider from "./ResizableDivider";
import CodeBlock from "./CodeBlock";

export interface TranscriptProps {
  userText: string;
  setUserText: (val: string) => void;
  onSendMessage: () => void;
  canSend: boolean;
  downloadRecording: () => void;
  // Optional props for multi-transcript mode
  transcriptItems?: TranscriptItem[];
  boxId?: string;
  isActiveBox?: boolean;
  parentScrollToBottom?: () => void;
  onClearTranscript?: () => void;
}

function ClientTranscript({
  userText,
  setUserText,
  onSendMessage,
  canSend,
  downloadRecording,
  transcriptItems: propTranscriptItems,
  boxId,
  isActiveBox = true,
  parentScrollToBottom,
  onClearTranscript,
}: TranscriptProps) {
  const transcriptContext = useTranscript();
  const { toggleTranscriptItemExpand: contextToggleTranscriptItemExpand } = transcriptContext;

  // Use either the provided transcriptItems or get them from the context
  const transcriptItems = propTranscriptItems || transcriptContext.transcriptItems;

  // Function to toggle transcript item expansion (works with both single and multi-transcript)
  const toggleTranscriptItemExpand = (itemId: string) => {
    if (boxId) {
      // If we're in multi-transcript mode, we need to use the MultiTranscriptContext
      // This would be handled by the parent component
      contextToggleTranscriptItemExpand(itemId);
    } else {
      // Otherwise use the regular TranscriptContext
      contextToggleTranscriptItemExpand(itemId);
    }
  };
  const transcriptRef = useRef<HTMLDivElement | null>(null);
  const [prevLogs, setPrevLogs] = useState<TranscriptItem[]>([]);
  const [justCopied, setJustCopied] = useState(false);
  const [isAtBottom, setIsAtBottom] = useState(true);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);
  const [userHasManuallyScrolled, setUserHasManuallyScrolled] = useState(false);
  const inputRef = useRef<HTMLInputElement | null>(null);

  function scrollToTop(force = false) {
    console.log(`Attempting to scroll to top in box ${boxId || 'main'}, isActiveBox: ${isActiveBox}, autoScrollEnabled: ${autoScrollEnabled}, force: ${force}, userHasManuallyScrolled: ${userHasManuallyScrolled}`);

    // If auto-scroll is disabled or user has manually scrolled, and this is not a forced scroll, don't scroll
    if ((!autoScrollEnabled || userHasManuallyScrolled) && !force) {
      console.log(`Box ${boxId || 'main'} - Auto-scroll is disabled or user has manually scrolled, not scrolling`);
      setShowScrollButton(true);
      return;
    }

    // If we're in multi-transcript mode and have a parent scroll function, use it
    if (boxId && parentScrollToBottom) {
      console.log(`Using parent scroll function for box ${boxId}`);
      parentScrollToBottom();
    }

    // Always try to scroll this component's transcript ref as well
    if (transcriptRef.current) {
      // Check if user has manually scrolled down significantly
      const { scrollTop } = transcriptRef.current;
      const hasUserScrolledDown = scrollTop > 200;

      // If user has manually scrolled down significantly and this isn't a forced scroll,
      // don't auto-scroll and just show the scroll button instead
      if (hasUserScrolledDown && !force) {
        console.log(`Box ${boxId || 'main'} - User has scrolled down, showing scroll button instead of auto-scrolling`);
        setShowScrollButton(true);
        setUserHasManuallyScrolled(true);
        return;
      }

      // Force scroll to top with multiple attempts to ensure it works
      const scrollToTopHelper = (attempts = 0) => {
        if (transcriptRef.current) {
          // Log scroll information for debugging
          console.log(`Box ${boxId || 'main'} - Scroll attempt ${attempts}: scrollTop=${transcriptRef.current.scrollTop}`);

          try {
            // Use a small timeout to ensure the DOM has updated
            setTimeout(() => {
              if (transcriptRef.current) {
                // Use smooth scrolling for better user experience
                transcriptRef.current.scrollTo({
                  top: 0,
                  behavior: 'smooth'
                });

                // Log the result
                console.log(`Box ${boxId || 'main'} - After scroll: scrollTop=${transcriptRef.current.scrollTop}`);
              }
            }, 10);
          } catch (e) {
            console.error("Error during scroll:", e);
          }

          // If we're not at the top yet and we haven't tried too many times, try again
          // but only try a few times to avoid being too aggressive
          if (attempts < 3 && transcriptRef.current.scrollTop > 5) {
            console.log(`Box ${boxId || 'main'} - Not at top yet, trying again`);
            setTimeout(() => scrollToTopHelper(attempts + 1), 100 * (attempts + 1));
          }
        }
      };

      // Start the scroll attempts - but be less aggressive
      scrollToTopHelper();

      // Just one additional attempt with a delay
      setTimeout(() => scrollToTopHelper(1), 200);

      // One final attempt after a longer delay to catch any late DOM updates
      setTimeout(() => {
        if (transcriptRef.current) {
          // Check again if user has scrolled down in the meantime
          const { scrollTop } = transcriptRef.current;

          if (scrollTop <= 200) {
            console.log(`Box ${boxId || 'main'} - Final scroll attempt`);
            try {
              transcriptRef.current.scrollTo({
                top: 0,
                behavior: 'smooth'
              });
            } catch (e) {
              console.error("Error during final scroll:", e);
            }
          }
        }
      }, 500);
    }
  }

  // Keep the original scrollToBottom function for backward compatibility
  function scrollToBottom(force = false) {
    console.log(`Attempting to scroll to bottom in box ${boxId || 'main'}, isActiveBox: ${isActiveBox}, autoScrollEnabled: ${autoScrollEnabled}, force: ${force}, userHasManuallyScrolled: ${userHasManuallyScrolled}`);

    // If auto-scroll is disabled or user has manually scrolled, and this is not a forced scroll, don't scroll
    if ((!autoScrollEnabled || userHasManuallyScrolled) && !force) {
      console.log(`Box ${boxId || 'main'} - Auto-scroll is disabled or user has manually scrolled, not scrolling`);
      setShowScrollButton(true);
      return;
    }

    // If we're in multi-transcript mode and have a parent scroll function, use it
    if (boxId && parentScrollToBottom) {
      console.log(`Using parent scroll function for box ${boxId}`);
      parentScrollToBottom();
    }

    // Always try to scroll this component's transcript ref as well
    if (transcriptRef.current) {
      // Check if user has manually scrolled up significantly
      const { scrollTop, scrollHeight, clientHeight } = transcriptRef.current;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
      const hasUserScrolledUp = distanceFromBottom > 200;

      // If user has manually scrolled up significantly and this isn't a forced scroll,
      // don't auto-scroll and just show the scroll button instead
      if (hasUserScrolledUp && !force) {
        console.log(`Box ${boxId || 'main'} - User has scrolled up, showing scroll button instead of auto-scrolling`);
        setShowScrollButton(true);
        setUserHasManuallyScrolled(true);
        return;
      }

      try {
        // Use smooth scrolling for better user experience
        setTimeout(() => {
          if (transcriptRef.current) {
            transcriptRef.current.scrollTo({
              top: transcriptRef.current.scrollHeight,
              behavior: 'smooth'
            });
          }
        }, 10);
      } catch (e) {
        console.error("Error during scroll:", e);
      }
    }
  }

  useEffect(() => {
    // Force scroll to top on initial render for this transcript box
    if (prevLogs.length === 0 && transcriptItems.length > 0) {
      setTimeout(() => scrollToTop(), 100);
    }

    const hasNewMessage = transcriptItems.length > prevLogs.length;

    // Check if any message content has been updated (especially for streaming responses)
    const hasUpdatedMessage = transcriptItems.some((newItem, index) => {
      const oldItem = prevLogs[index];
      return (
        oldItem &&
        (newItem.title !== oldItem.title ||
         newItem.data !== oldItem.data ||
         newItem.status !== oldItem.status)
      );
    });

    // Check if the first message (after reversing) is from the assistant and is still in progress (streaming)
    // Note: Since we're displaying in reverse order, we need to check the first message
    const firstMessage = transcriptItems[0];
    const isAssistantStreaming = firstMessage?.role === 'assistant' && firstMessage?.status === 'IN_PROGRESS';

    // Check if it's a new user message
    const isNewUserMessage = hasNewMessage && firstMessage?.role === 'user';

    // Always scroll in these cases:
    // 1. This is the active box and there's a new or updated message AND auto-scroll is enabled
    // 2. User is already at the top and there's a new or updated message AND auto-scroll is enabled
    // 3. It's a new user message (user just sent something) AND auto-scroll is enabled
    // 4. Assistant is currently streaming a response AND auto-scroll is enabled
    // 5. User has not manually scrolled down
    if (
      ((isActiveBox && (hasNewMessage || hasUpdatedMessage)) ||
      (isAtBottom && (hasNewMessage || hasUpdatedMessage)) ||
      isNewUserMessage ||
      isAssistantStreaming) &&
      autoScrollEnabled &&
      !userHasManuallyScrolled
    ) {
      // Use multiple scroll attempts with different delays to ensure it works
      scrollToTop();
      setTimeout(() => scrollToTop(), 100);
      setTimeout(() => scrollToTop(), 300);

      // For streaming responses, set up a more careful scroll that respects user scrolling
      if (isAssistantStreaming) {
        // Instead of a continuous forced scroll, we'll use a smarter approach
        // that only scrolls if the user hasn't manually scrolled down
        let userHasScrolled = false;

        // Track the last scroll position to detect direction
        let lastScrollTop = 0;

        // Set up a scroll event listener to detect manual scrolling
        const detectUserScroll = () => {
          if (transcriptRef.current) {
            const { scrollTop } = transcriptRef.current;

            // Determine scroll direction (up or down)
            const isScrollingDown = scrollTop > lastScrollTop;

            // Update the last scroll position
            lastScrollTop = scrollTop;

            // If user is explicitly scrolling down, mark as manual scroll
            if (isScrollingDown) {
              userHasScrolled = true;
              setUserHasManuallyScrolled(true);
              console.log("User has manually scrolled down during streaming, pausing auto-scroll");
            } else if (scrollTop < 100) {
              // User has scrolled to the top
              userHasScrolled = false;

              // Only reset manual scroll flag if auto-scroll is enabled
              if (autoScrollEnabled) {
                setUserHasManuallyScrolled(false);
              }
            }

            // Debug logging
            console.log(`Streaming scroll detection - position: ${scrollTop}, direction: ${isScrollingDown ? 'DOWN' : 'UP'}, userHasScrolled: ${userHasScrolled}, userHasManuallyScrolled: ${userHasManuallyScrolled}`);
          }
        };

        if (transcriptRef.current) {
          transcriptRef.current.addEventListener('scroll', detectUserScroll);
        }

        // Set up a gentler interval that respects user scrolling
        const scrollInterval = setInterval(() => {
          if (transcriptRef.current) {
            // Get the current state of manual scrolling and auto-scroll
            const manuallyScrolled = userHasManuallyScrolled;
            const currentAutoScrollState = autoScrollEnabled;

            console.log(`Scroll interval check - autoScrollEnabled: ${currentAutoScrollState}, userHasScrolled: ${userHasScrolled}, userHasManuallyScrolled: ${manuallyScrolled}`);

            // Only auto-scroll if:
            // 1. User hasn't manually scrolled down (local variable)
            // 2. User hasn't manually scrolled down (React state)
            // 3. Auto-scroll is enabled
            if (!userHasScrolled && !manuallyScrolled && currentAutoScrollState) {
              console.log("Auto-scrolling during streaming");
              transcriptRef.current.scrollTop = 0;
            } else {
              // If auto-scroll is disabled or user has scrolled down, show the scroll button
              setShowScrollButton(true);
              console.log("Not auto-scrolling during streaming - manual scroll or auto-scroll disabled");
            }
          } else {
            clearInterval(scrollInterval);
          }
        }, 300); // Less frequent checks

        // Clear the interval and remove event listener after a reasonable time
        setTimeout(() => {
          clearInterval(scrollInterval);
          if (transcriptRef.current) {
            transcriptRef.current.removeEventListener('scroll', detectUserScroll);
          }
        }, 5000);
      }
    } else if (hasNewMessage || hasUpdatedMessage) {
      // If there's a new message but we're not scrolling, show the scroll button
      setShowScrollButton(true);
    }

    setPrevLogs(transcriptItems);
  }, [transcriptItems, isAtBottom, isActiveBox, autoScrollEnabled, userHasManuallyScrolled]);

  // Autofocus on text box input on load
  useEffect(() => {
    if (canSend && inputRef.current) {
      inputRef.current.focus();
    }
  }, [canSend]);

  // Scroll to top on initial render and set up MutationObserver
  useEffect(() => {
    // Initial scroll to top
    setTimeout(scrollToTop, 100);
    setTimeout(scrollToTop, 300);

    // Also scroll to top when window is resized
    const handleResize = () => {
      setTimeout(scrollToTop, 100);
    };

    // Set up MutationObserver to detect when new content is added
    const observer = new MutationObserver((mutations) => {
      // Check if any of the mutations involve text content changes
      const hasTextChanges = mutations.some(mutation =>
        mutation.type === 'characterData' ||
        mutation.addedNodes.length > 0 ||
        (mutation.type === 'attributes' && mutation.attributeName === 'class')
      );

      if (hasTextChanges) {
        console.log("MutationObserver detected content changes");

        // Only scroll if auto-scroll is enabled and we're at the top or if this is the active box
        if ((isAtBottom || isActiveBox) && autoScrollEnabled && !userHasManuallyScrolled) {
          scrollToTop();
          setTimeout(scrollToTop, 100);
        } else {
          setShowScrollButton(true);
        }
      }
    });

    // Start observing the transcript container with all possible options
    if (transcriptRef.current) {
      observer.observe(transcriptRef.current, {
        childList: true,
        subtree: true,
        characterData: true,
        characterDataOldValue: true,
        attributes: true,
        attributeOldValue: true
      });

      // Also observe the parent element to catch any structural changes
      if (transcriptRef.current.parentElement) {
        observer.observe(transcriptRef.current.parentElement, {
          childList: true,
          subtree: false
        });
      }
    }

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      observer.disconnect();
    };
  }, [isAtBottom, isActiveBox, autoScrollEnabled, userHasManuallyScrolled]);

  // Create a ref to track the last scroll position outside of useEffect
  const lastScrollTopRef = useRef<number>(0);

  // For tracking down arrow key press duration
  const downKeyPressStartRef = useRef<number | null>(null);
  const downKeyLongPressThreshold = 500; // ms threshold for long press

  // Add scroll event listener to detect when user is not at bottom
  useEffect(() => {
    const handleScroll = () => {
      if (transcriptRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = transcriptRef.current;
        const lastScrollTop = lastScrollTopRef.current;

        // Update the last scroll position
        lastScrollTopRef.current = scrollTop;

        // Determine scroll direction (up or down)
        const isScrollingUp = scrollTop < lastScrollTop;

        // Consider "at bottom" if within 100px of the bottom
        // This makes auto-scrolling more reliable by being more lenient
        const isScrolledToBottom = scrollHeight - scrollTop - clientHeight < 100;

        // Detect if user has scrolled up significantly
        const hasScrolledUpSignificantly = scrollHeight - scrollTop - clientHeight > 200;

        // If user is explicitly scrolling up, mark as manual scroll
        if (isScrollingUp) {
          console.log("Detected manual scroll UP");
          setUserHasManuallyScrolled(true);
        } else if (isScrolledToBottom) {
          // If user has scrolled all the way to the bottom, they're probably done reading
          // earlier content, so we can reset the manual scroll flag
          setUserHasManuallyScrolled(false);
        }

        setIsAtBottom(isScrolledToBottom);

        // Only show scroll button if we're significantly away from the bottom
        setShowScrollButton(scrollHeight - scrollTop - clientHeight > 150);

        // Debug logging
        console.log(`Scroll position: ${scrollTop}, direction: ${isScrollingUp ? 'UP' : 'DOWN'}, isAtBottom: ${isScrolledToBottom}, userHasManuallyScrolled: ${userHasManuallyScrolled}, autoScrollEnabled: ${autoScrollEnabled}`);
      }
    };

    const transcriptElement = transcriptRef.current;
    if (transcriptElement) {
      transcriptElement.addEventListener('scroll', handleScroll);

      // Also listen for wheel events to detect user scrolling
      const handleWheel = (e: WheelEvent) => {
        // When user scrolls with the wheel, we'll mark it as manual scrolling
        // We need to check the direction of the wheel event
        if (transcriptRef.current) {
          // Positive deltaY means scrolling down, negative means scrolling up
          const isScrollingUp = e.deltaY < 0;

          if (isScrollingUp) {
            console.log("Wheel event detected scrolling UP");
            // User is explicitly scrolling up, mark as manual scroll
            setUserHasManuallyScrolled(true);

            // Show the scroll button since we're moving away from the bottom
            setShowScrollButton(true);
            setIsAtBottom(false);
          } else {
            // User is scrolling down
            const { scrollTop, scrollHeight, clientHeight } = transcriptRef.current;
            const isScrolledToBottom = scrollHeight - scrollTop - clientHeight < 100;

            if (isScrolledToBottom) {
              // If user manually scrolls to the bottom, we can reset the manual scroll flag
              console.log("User scrolled to bottom, resetting manual scroll flag");
              setUserHasManuallyScrolled(false);
              setIsAtBottom(true);
            }
          }
        }
      };

      transcriptElement.addEventListener('wheel', handleWheel);

      // Handle keyboard shortcuts for auto-scroll and clear
      const handleKeyDown = (e: KeyboardEvent) => {
        // Only handle arrow keys when not typing in an input field
        if (e.target instanceof HTMLElement &&
            e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {

          // Up arrow key disables auto-scroll when auto-scroll is enabled
          if (e.key === 'ArrowUp') {
            if (autoScrollEnabled) {
              console.log('Up arrow pressed - disabling auto-scroll');
              setAutoScrollEnabled(false);
              setUserHasManuallyScrolled(true);
              // Don't prevent default scrolling - allow normal scrolling behavior
            }
          }

          // Down arrow key starts tracking for long press
          if (e.key === 'ArrowDown') {
            if (!downKeyPressStartRef.current) {
              downKeyPressStartRef.current = Date.now();
            }
            // Don't prevent default scrolling - allow normal scrolling behavior
          }

          // Left arrow key triggers clear transcript
          if (e.key === 'ArrowLeft' && onClearTranscript) {
            console.log('Left arrow pressed - clearing transcript');
            onClearTranscript();
            e.preventDefault(); // Prevent default scrolling
          }
        }
      };

      const handleKeyUp = (e: KeyboardEvent) => {
        // Handle down arrow key release
        if (e.key === 'ArrowDown' && downKeyPressStartRef.current) {
          const pressDuration = Date.now() - downKeyPressStartRef.current;

          // If it was a long press, enable auto-scroll
          if (pressDuration >= downKeyLongPressThreshold) {
            if (!autoScrollEnabled) {
              console.log('Down arrow long press detected - enabling auto-scroll');
              setAutoScrollEnabled(true);
              setUserHasManuallyScrolled(false);
              scrollToTop(true); // Force scroll to top
              e.preventDefault(); // Only prevent default for long press activation
            }
          }

          // Reset the press start time
          downKeyPressStartRef.current = null;
        }
      };

      // Add keyboard event listeners
      window.addEventListener('keydown', handleKeyDown);
      window.addEventListener('keyup', handleKeyUp);

      // Initial check
      handleScroll();

      // Set a periodic check to ensure scroll position is correctly detected
      const intervalCheck = setInterval(handleScroll, 1000);

      return () => {
        if (transcriptElement) {
          transcriptElement.removeEventListener('scroll', handleScroll);
          transcriptElement.removeEventListener('wheel', handleWheel);
        }
        window.removeEventListener('keydown', handleKeyDown);
        window.removeEventListener('keyup', handleKeyUp);
        clearInterval(intervalCheck);
      };
    }

    return undefined;
  }, [autoScrollEnabled, scrollToBottom]); // Add dependencies

  const handleCopyTranscript = async () => {
    if (!transcriptRef.current) return;
    try {
      await navigator.clipboard.writeText(transcriptRef.current.innerText);
      setJustCopied(true);
      setTimeout(() => setJustCopied(false), 1500);
    } catch (error) {
      console.error("Failed to copy transcript:", error);
    }
  };

  return (
    <div className="flex flex-col h-full w-full bg-white rounded-xl overflow-hidden" style={{ display: 'flex', flexDirection: 'column', height: '100%', width: '100%', maxHeight: '100vh' }}>
      {/* Header - Fixed height */}
      <div className="flex-shrink-0 flex items-center justify-between px-4 py-2 text-base border-b bg-white rounded-t-xl">
        <div className="flex items-center gap-x-2">
          <span className="font-semibold text-lg">Transcript</span>
          {onClearTranscript && (
            <button
              onClick={onClearTranscript}
              className="text-sm px-2 py-1 rounded-md bg-red-100 hover:bg-red-200 text-red-700 flex items-center justify-center"
              title="Clear transcript (or press Left Arrow key)"
            >
              Clear
            </button>
          )}
        </div>
        <div className="flex gap-x-2">
          {/* Auto-scroll toggle button in header */}
          <button
            onClick={() => {
              const newState = !autoScrollEnabled;
              console.log(`Auto-scroll ${newState ? 'enabled' : 'disabled'} in box ${boxId || 'main'}`);
              setAutoScrollEnabled(newState);

              // Always reset the manual scroll flag when toggling
              setUserHasManuallyScrolled(false);

              // If enabling auto-scroll, scroll to top immediately
              if (newState) {
                console.log("Auto-scroll enabled, forcing scroll to top");
                scrollToTop(true);
              }
            }}
            className={`text-sm px-3 py-1 rounded-md ${autoScrollEnabled ? 'bg-green-600' : 'bg-red-600'} text-white flex items-center justify-center gap-x-1`}
            aria-label={autoScrollEnabled ? "Disable auto-scroll" : "Enable auto-scroll"}
            title={autoScrollEnabled ? "Auto-scroll is ON (click or press Up Arrow to disable)" : "Auto-scroll is OFF (click to enable or hold Down Arrow for 0.5s)"}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              {autoScrollEnabled ? (
                <>
                  <polyline points="7 11 12 6 17 11" />
                  <polyline points="7 17 12 12 17 17" />
                </>
              ) : (
                <>
                  <line x1="12" y1="5" x2="12" y2="19" />
                  <polyline points="19 12 12 19 5 12" />
                </>
              )}
            </svg>
            <span>Auto-scroll: {autoScrollEnabled ? 'ON' : 'OFF'}</span>
          </button>

          <button
            onClick={handleCopyTranscript}
            className="w-24 text-sm px-2 py-1 rounded-md bg-gray-200 hover:bg-gray-300 flex items-center justify-center gap-x-1"
          >
            <ClipboardCopyIcon width={16} height={16} />
            {justCopied ? "Copied!" : "Copy"}
          </button>
          <button
            onClick={downloadRecording}
            className="w-40 text-sm px-2 py-1 rounded-md bg-gray-200 hover:bg-gray-300 flex items-center justify-center gap-x-1"
          >
            <DownloadIcon width={16} height={16} />
            <span>Download Audio</span>
          </button>
        </div>
      </div>

      {/* Transcript Content - Scrollable area */}
      <div
        ref={transcriptRef}
        className="flex-grow overflow-y-auto p-4 flex flex-col gap-y-2 relative bg-gray-100 w-full"
        style={{
          scrollBehavior: 'smooth',
          flex: 1,
          overflowY: 'auto',
          overflowX: 'hidden',
          willChange: 'scroll-position',
          height: '100%', // Ensure it takes full height
          width: '100%' // Ensure it takes full width
        }}
      >
        {/* No greeting message */}


          {/* Scroll to top button - only visible when not at top */}
          {showScrollButton && (
            <button
              onClick={() => {
                console.log(`Scroll button clicked in box ${boxId || 'main'}`);
                scrollToTop(true);
                // Also use parent scroll if available
                if (boxId && parentScrollToBottom) {
                  parentScrollToBottom();
                }
                setShowScrollButton(false);
                setUserHasManuallyScrolled(false);
              }}
              className="absolute bottom-6 right-6 bg-gray-800 text-white p-4 rounded-full shadow-xl hover:bg-gray-700 transition-colors z-20"
              aria-label="Scroll to top"
              style={{ animation: 'pulse 2s infinite' }}
            >
              <ArrowUpIcon width={24} height={24} />
            </button>
          )}

          {/* Add some CSS for the pulse animation */}
          <style jsx>{`
            @keyframes pulse {
              0% { transform: scale(1); }
              50% { transform: scale(1.1); }
              100% { transform: scale(1); }
            }
          `}</style>
          {[...transcriptItems].reverse().map((item) => {
            const {
              itemId,
              type,
              role,
              data,
              expanded,
              timestamp,
              title = "",
              isHidden,
              guardrailResult,
            } = item;

            if (isHidden) {
              return null;
            }

            if (type === "MESSAGE") {
              const isUser = role === "user";
              const containerClasses = `flex flex-col ${
                isUser ? "items-end" : "items-start"
              } w-full`;
              // Use a different background for transcribing messages
              const isTranscribingMessage = title.includes("[Transcribing");
              const bubbleBase = `w-full p-3 ${
                isUser                ? isTranscribingMessage
                    ? "bg-gray-400 text-gray-900"
                    : "bg-gray-500 text-gray-100"
                  : "bg-gray-100 text-black"
              }`;
              const isBracketedMessage =
                title.startsWith("[") && title.endsWith("]");
              const messageStyle = isBracketedMessage
                ? isTranscribingMessage
                  ? "italic text-gray-500"
                  : "italic text-gray-400"
                : "";
              const displayTitle = isBracketedMessage
                ? title.slice(1, -1)
                : title;

              return (
                <div key={itemId} className={`${containerClasses} mb-2`}>
                  <div className={`max-w-full w-full ${isUser ? '' : ''}`}>
                    <div
                      className={`${bubbleBase} rounded-t-xl ${
                        guardrailResult && guardrailResult.category !== "NONE" ? "" : "rounded-b-xl"
                      } shadow-sm`}
                    >                      <div
                        className={`text-xs ${
                          isUser ? "text-gray-400" : "text-gray-500"
                        } font-mono mb-0.5`}
                      >
                        {timestamp}
                      </div>
                      <div className={`whitespace-pre-wrap ${isUser ? 'text-base' : 'text-lg'} ${messageStyle} leading-snug`}>
                        <ReactMarkdown
                          components={{
                            code({node, inline, className, children, ...props}) {
                              const match = /language-(\w+)/.exec(className || '');
                              return !inline && match ? (
                                <CodeBlock
                                  language={match[1]}
                                  value={String(children).replace(/\n$/, '')}
                                />
                              ) : (
                                <code className={className} {...props}>
                                  {children}
                                </code>
                              );
                            },                            p: ({children}) => <p className="my-0.5">{children}</p>,
                            ul: ({children}) => <ul className="my-0.5 ml-4">{children}</ul>,
                            ol: ({children}) => <ol className="my-0.5 ml-4">{children}</ol>,
                            li: ({children}) => <li className="my-0.25">{children}</li>,                            h1: ({children}) => <h1 className="text-2xl font-bold my-1">{children}</h1>,
                            h2: ({children}) => <h2 className="text-xl font-bold my-1">{children}</h2>,
                            h3: ({children}) => <h3 className="text-lg font-bold my-0.5">{children}</h3>,
                            h4: ({children}) => <h4 className="text-base font-bold my-0.5">{children}</h4>,
                            blockquote: ({children}) => <blockquote className="border-l-4 border-gray-300 pl-2 my-1 italic">{children}</blockquote>,
                          }}
                        >
                          {displayTitle}
                        </ReactMarkdown>
                      </div>
                    </div>
                    {guardrailResult && guardrailResult.category !== "NONE" && (
                      <div className="bg-gray-200 px-3 py-2 rounded-b-xl">
                        <GuardrailChip guardrailResult={guardrailResult} />
                      </div>
                    )}
                  </div>
                </div>
              );
            } else if (type === "BREADCRUMB") {
              return (
                <div
                  key={itemId}
                  className="flex flex-col justify-start items-start text-gray-500 text-sm mb-1"
                >
                  <span className="text-xs font-mono">{timestamp}</span>
                  <div
                    className={`whitespace-pre-wrap flex items-center font-mono text-sm text-gray-800 ${
                      data ? "cursor-pointer" : ""
                    }`}
                    onClick={() => data && toggleTranscriptItemExpand(itemId)}
                  >
                    {data && (
                      <span
                        className={`text-gray-400 mr-1 transform transition-transform duration-200 select-none font-mono ${
                          expanded ? "rotate-90" : "rotate-0"
                        }`}
                      >
                        ▶
                      </span>
                    )}
                    {title}
                  </div>
                  {expanded && data && (
                    <div className="text-gray-800 text-left">
                      <pre className="border-l-2 ml-1 border-gray-200 whitespace-pre-wrap break-words font-mono text-xs mb-0.5 mt-0.5 pl-2 py-0.5 leading-tight">
                        {JSON.stringify(data, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              );
            } else {
              // Fallback if type is neither MESSAGE nor BREADCRUMB
              return (
                <div
                  key={itemId}
                  className="flex justify-center text-gray-500 text-xs italic font-mono"
                >
                  Unknown item type: {type}{" "}
                  <span className="ml-2 text-xs">{timestamp}</span>
                </div>
              );
            }
          })}
        </div>

      {/* Input area - Fixed height */}
      <div className="flex-shrink-0 p-4 flex items-center gap-x-3 border-t border-gray-200">
        <input
          ref={inputRef}          type="text"
          value={userText}
          onChange={React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
            setUserText(e.target.value);
          }, [setUserText])}
          onKeyDown={React.useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
            if (e.key === "Enter" && canSend) {
              onSendMessage();
              // Force scroll to top when sending a message
              setTimeout(scrollToTop, 100);
            }
          }, [canSend, onSendMessage, scrollToTop])}
          className="flex-1 px-4 py-3 text-lg border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Type a message..."
        />
        <button
          onClick={() => {
            onSendMessage();
            // Force scroll to top when sending a message
            setTimeout(scrollToTop, 100);
          }}
          disabled={!canSend || !userText.trim()}
          className="bg-blue-400 text-white rounded-full p-2 disabled:opacity-50 hover:bg-blue-500 transition-colors"
        >
          <Image src="arrow.svg" alt="Send" width={24} height={24} />
        </button>
      </div>
    </div>
  );
}

export default ClientTranscript;
