#!/bin/bash

# Load configuration
PORT=$(grep "^PORT=" config.properties | cut -d'=' -f2)

# Check if PORT is empty, use default if it is
if [ -z "$PORT" ]; then
  PORT=3000
  echo "No port specified in config.properties, using default port $PORT"
else
  echo "Using port $PORT from config.properties"
fi

# Check if anything is running on the specified port
PID=$(lsof -i :$PORT -t 2>/dev/null)

if [ ! -z "$PID" ]; then
  echo "Process with PID $PID is using port $PORT. Attempting to terminate..."
  kill -15 $PID
  sleep 2
  
  # Check if process is still running
  if ps -p $PID > /dev/null; then
    echo "Process is still running. Forcing termination..."
    kill -9 $PID
    sleep 1
  fi
  
  echo "Port $PORT is now available."
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  npm install
fi

# Start the application
echo "Starting OpenAI Realtime Agents Demo on port $PORT..."
npm run dev -- -p $PORT

# Open browser (platform specific)
if [[ "$OSTYPE" == "darwin"* ]]; then
  # macOS
  open http://localhost:$PORT
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
  # Linux
  xdg-open http://localhost:$PORT &>/dev/null || sensible-browser http://localhost:$PORT &>/dev/null || x-www-browser http://localhost:$PORT &>/dev/null || gnome-open http://localhost:$PORT &>/dev/null
else
  echo "Please open your browser and navigate to http://localhost:$PORT"
fi
