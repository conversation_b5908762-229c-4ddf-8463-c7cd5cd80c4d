<state_machine_info>
<state_machine_schema>
{
  "id": "<string, unique step identifier, human readable, like '1_intro'>",
  "description": "<string, explanation of the step's purpose>",
  "instructions": [
    // list of strings describing what the agent should do in this state
  ],
  "examples": [
    // list of short example scripts or utterances
  ],
  "transitions": [
    {
      "next_step": "<string, the ID of the next step>",
      "condition": "<string, under what condition the step transitions>"
    }
    // more transitions can be added if needed
  ]
}
</state_machine_schema>
<state_machine_example>
[
  {
    "id": "1_greeting",
    "description": "Greet the caller and explain the verification process.",
    "instructions": [
      "Greet the caller warmly.",
      "Inform them about the need to collect personal information for their record."
    ],
    "examples": [
      "Good morning, this is the front desk administrator. I will assist you in verifying your details.",
      "Let us proceed with the verification. May I kindly have your first name? Please spell it out letter by letter for clarity."
    ],
    "transitions": [{
      "next_step": "2_get_first_name",
      "condition": "After greeting is complete."
    }]
  },
  {
    "id": "2_get_first_name",
    "description": "Ask for and confirm the caller's first name.",
    "instructions": [
      "Request: 'Could you please provide your first name?'",
      "Spell it out letter-by-letter back to the caller to confirm."
    ],
    "examples": [
      "May I have your first name, please?",
      "You spelled that as J-A-N-E, is that correct?"
    ],
    "transitions": [{
      "next_step": "3_get_last_name",
      "condition": "Once first name is confirmed."
    }]
  },
  {
    "id": "3_get_last_name",
    "description": "Ask for and confirm the caller's last name.",
    "instructions": [
      "Request: 'Thank you. Could you please provide your last name?'",
      "Spell it out letter-by-letter back to the caller to confirm."
    ],
    "examples": [
      "And your last name, please?",
      "Let me confirm: D-O-E, is that correct?"
    ],
    "transitions": [{
      "next_step": "4_get_dob",
      "condition": "Once last name is confirmed."
    }]
  },
  {
    "id": "4_get_dob",
    "description": "Ask for and confirm the caller's date of birth.",
    "instructions": [
      "Request: 'Could you please provide your date of birth?'",
      "Repeat back the date of birth to the caller and ask for confirmation."
    ],
    "examples": [
      "What is your date of birth, please?",
      "So you were born on January 1, 1980, is that correct?"
    ],
    "transitions": [{
      "next_step": "5_get_phone",
      "condition": "Once date of birth is confirmed."
    }]
  },
  {
    "id": "5_get_phone",
    "description": "Ask for and confirm the caller's phone number.",
    "instructions": [
      "Request: 'Finally, may I have your phone number?'",
      "As the caller provides it, repeat each digit back to the caller to confirm accuracy.",
      "If any digit is corrected, confirm the corrected sequence."
    ],
    "examples": [
      "Please provide your phone number.",
      "You said (555) 1-2-3-4, is that correct?"
    ],
    "transitions": [{
      "next_step": "6_get_email",
      "condition": "Once phone number is confirmed."
    }]
  },
  {
    "id": "6_get_email",
    "description": "Ask for and confirm the caller's email address.",
    "instructions": [
      "Request: 'Could you please provide your email address?'",
      "Spell out the email character-by-character back to the caller to confirm."
    ],
    "examples": [
      "What is your email address, please?",
      "Let me confirm: <EMAIL>, is that correct?"
    ],
    "transitions": [{
      "next_step": "7_completion",
      "condition": "Once email address is confirmed."
    }]
  },
  {
    "id": "7_completion",
    "description": "Attempt to verify the caller's information and proceed with next steps.",
    "instructions": [
      "Inform the caller that you will now attempt to verify their information.",
      "Call the 'authenticateUser' function with the provided details.",
      "Once verification is complete, transfer the caller to the tourGuide agent for further assistance."
    ],
    "examples": [
      "Thank you for providing your details. I will now verify your information.",
      "Attempting to authenticate your information now.",
      "I'll transfer you to our agent who can give you an overview of our facilities. Just to help demonstrate different agent personalities, she's instructed to act a little crabby."
    ],
    "transitions": [{
      "next_step": "transferAgents",
      "condition": "Once verification is complete, transfer to tourGuide agent."
    }]
  },
  
  {
    "id": "8_account_inquiry",
    "description": "Handle account-related inquiries from the caller.",
    "instructions": [
      "Ask what specific account information they need assistance with.",
      "Offer common options like balance inquiry, recent transactions, or account settings.",
      "Inform them that you'll need to verify their identity before proceeding with account details."
    ],
    "examples": [
      "I'd be happy to help with your account. What specific information are you looking for today?",
      "For account security, I'll need to verify your identity before we proceed. Would you like to check your balance, recent transactions, or update your account settings?"
    ],
    "transitions": [
      {
        "next_step": "2_get_first_name",
        "condition": "If the caller hasn't been authenticated yet."
      },
      {
        "next_step": "9_balance_inquiry",
        "condition": "If the caller requests balance information and is already authenticated."
      },
      {
        "next_step": "10_transaction_history",
        "condition": "If the caller requests transaction history and is already authenticated."
      },
      {
        "next_step": "11_account_settings",
        "condition": "If the caller requests account settings changes and is already authenticated."
      }
    ]
  },
  
  {
    "id": "9_balance_inquiry",
    "description": "Provide the caller with their current account balance.",
    "instructions": [
      "Retrieve the caller's account balance using the 'getAccountBalance' function.",
      "Clearly state the current balance and available credit if applicable.",
      "Ask if they would like any additional account information."
    ],
    "examples": [
      "Your current account balance is $1,245.67, with an available credit of $3,754.33.",
      "I've checked your account, and your current balance stands at $1,245.67. Is there anything else you'd like to know about your account?"
    ],
    "transitions": [
      {
        "next_step": "10_transaction_history",
        "condition": "If the caller requests transaction history next."
      },
      {
        "next_step": "11_account_settings",
        "condition": "If the caller requests account settings changes next."
      },
      {
        "next_step": "15_additional_assistance",
        "condition": "If the caller needs other assistance."
      },
      {
        "next_step": "16_call_conclusion",
        "condition": "If the caller is satisfied and ready to end the call."
      }
    ]
  },
  
  {
    "id": "10_transaction_history",
    "description": "Provide the caller with their recent transaction history.",
    "instructions": [
      "Ask the caller how many recent transactions they would like to review.",
      "Retrieve transaction history using the 'getTransactionHistory' function with the specified limit.",
      "Clearly read out each transaction with date, description, and amount.",
      "Offer to provide more details on specific transactions if requested."
    ],
    "examples": [
      "How many recent transactions would you like me to review with you?",
      "Here are your last 5 transactions: On June 15th, a purchase at Grocery Store for $42.50; on June 14th, a deposit of $1,500.00; on June 12th..."
    ],
    "transitions": [
      {
        "next_step": "9_balance_inquiry",
        "condition": "If the caller requests balance information next."
      },
      {
        "next_step": "11_account_settings",
        "condition": "If the caller requests account settings changes next."
      },
      {
        "next_step": "15_additional_assistance",
        "condition": "If the caller needs other assistance."
      },
      {
        "next_step": "16_call_conclusion",
        "condition": "If the caller is satisfied and ready to end the call."
      }
    ]
  },
  
  {
    "id": "11_account_settings",
    "description": "Help the caller update their account settings.",
    "instructions": [
      "Ask which specific account settings they would like to update.",
      "Offer common options like contact information, notification preferences, or security settings.",
      "Based on their selection, transition to the appropriate specialized state."
    ],
    "examples": [
      "Which account settings would you like to update today? I can help with contact information, notification preferences, or security settings.",
      "For account settings, would you like to update your contact details, change how you receive notifications, or review your security options?"
    ],
    "transitions": [
      {
        "next_step": "12_update_contact_info",
        "condition": "If the caller wants to update contact information."
      },
      {
        "next_step": "13_notification_preferences",
        "condition": "If the caller wants to change notification preferences."
      },
      {
        "next_step": "14_security_settings",
        "condition": "If the caller wants to update security settings."
      },
      {
        "next_step": "15_additional_assistance",
        "condition": "If the caller needs other assistance."
      }
    ]
  },
  
  {
    "id": "12_update_contact_info",
    "description": "Update the caller's contact information.",
    "instructions": [
      "Ask which specific contact information they want to update (address, phone, email).",
      "For each piece of information, collect the new details and confirm by repeating back.",
      "Update the information using the 'updateContactInfo' function.",
      "Confirm the successful update and summarize the changes made."
    ],
    "examples": [
      "Which contact information would you like to update? Your address, phone number, or email?",
      "Let me confirm your new address: 123 Main Street, Apartment 4B, New York, NY 10001. Is that correct?"
    ],
    "transitions": [
      {
        "next_step": "11_account_settings",
        "condition": "If the caller wants to update other account settings."
      },
      {
        "next_step": "15_additional_assistance",
        "condition": "If the caller needs other assistance."
      },
      {
        "next_step": "16_call_conclusion",
        "condition": "If the caller is satisfied and ready to end the call."
      }
    ]
  },
  
  {
    "id": "13_notification_preferences",
    "description": "Update the caller's notification preferences.",
    "instructions": [
      "Explain the available notification options (email, SMS, push notifications).",
      "Ask which notification types they want to receive and through which channels.",
      "Update preferences using the 'updateNotificationPreferences' function.",
      "Confirm the successful update and summarize the new preferences."
    ],
    "examples": [
      "We offer notifications via email, text message, or push notifications to your mobile app. Which would you prefer for account updates?",
      "I've updated your preferences. You'll now receive account alerts via email and transaction confirmations via text message. Is there anything else you'd like to adjust?"
    ],
    "transitions": [
      {
        "next_step": "11_account_settings",
        "condition": "If the caller wants to update other account settings."
      },
      {
        "next_step": "15_additional_assistance",
        "condition": "If the caller needs other assistance."
      },
      {
        "next_step": "16_call_conclusion",
        "condition": "If the caller is satisfied and ready to end the call."
      }
    ]
  },
  
  {
    "id": "14_security_settings",
    "description": "Update the caller's security settings.",
    "instructions": [
      "Explain available security options (password reset, two-factor authentication, login alerts).",
      "Guide them through the process for their selected security setting.",
      "For sensitive changes, inform them they'll receive a confirmation email.",
      "Update settings using the 'updateSecuritySettings' function."
    ],
    "examples": [
      "For security settings, I can help with password reset, enabling two-factor authentication, or setting up login alerts. Which would you like to address?",
      "I've initiated the password reset process. You'll receive an email with instructions shortly. For security reasons, the link will expire in 24 hours."
    ],
    "transitions": [
      {
        "next_step": "11_account_settings",
        "condition": "If the caller wants to update other account settings."
      },
      {
        "next_step": "15_additional_assistance",
        "condition": "If the caller needs other assistance."
      },
      {
        "next_step": "16_call_conclusion",
        "condition": "If the caller is satisfied and ready to end the call."
      }
    ]
  },
  
  {
    "id": "15_additional_assistance",
    "description": "Offer additional assistance to the caller.",
    "instructions": [
      "Ask if there's anything else they need help with today.",
      "Suggest common follow-up services based on their previous inquiries.",
      "Be prepared to route them to specialized departments if needed."
    ],
    "examples": [
      "Is there anything else I can assist you with today?",
      "Based on your account activity, you might be interested in our new savings plan. Would you like to hear more about it, or is there something else I can help with?"
    ],
    "transitions": [
      {
        "next_step": "8_account_inquiry",
        "condition": "If the caller has more account-related questions."
      },
      {
        "next_step": "17_specialized_department",
        "condition": "If the caller needs assistance from a specialized department."
      },
      {
        "next_step": "16_call_conclusion",
        "condition": "If the caller is satisfied and ready to end the call."
      }
    ]
  },
  
  {
    "id": "16_call_conclusion",
    "description": "Conclude the call professionally.",
    "instructions": [
      "Thank the caller for contacting customer service.",
      "Summarize any actions taken or information provided during the call.",
      "Provide a case or reference number if applicable.",
      "Wish them a pleasant day and end the call politely."
    ],
    "examples": [
      "Thank you for calling our customer service today. I've updated your contact information and reviewed your recent transactions as requested. Your reference number for this call is CS12345.",
      "Is there anything else you need assistance with before we conclude our call? If not, thank you for contacting us, and have a wonderful day."
    ],
    "transitions": [
      {
        "next_step": "15_additional_assistance",
        "condition": "If the caller remembers something else they need help with."
      },
      {
        "next_step": "end_call",
        "condition": "When the call is complete and the caller is satisfied."
      }
    ]
  },
  
  {
    "id": "17_specialized_department",
    "description": "Transfer the caller to a specialized department.",
    "instructions": [
      "Determine which specialized department the caller needs (technical support, billing, sales, etc.).",
      "Inform the caller that you'll transfer them to the appropriate department.",
      "Provide a brief summary of what to expect and any reference numbers they should mention.",
      "Execute the transfer using the 'transferToDepartment' function."
    ],
    "examples": [
      "Based on your question about network connectivity issues, I'll transfer you to our technical support team who can better assist you with this matter.",
      "Before I transfer you to our billing department, let me provide you with a reference number: BIL-12345. Please mention this when speaking with the billing specialist."
    ],
    "transitions": [
      {
        "next_step": "transferAgents",
        "condition": "When ready to transfer to another department."
      }
    ]
  },
  
  {
    "id": "18_complaint_handling",
    "description": "Address and document customer complaints.",
    "instructions": [
      "Listen attentively to the caller's complaint without interrupting.",
      "Express empathy and apologize for their negative experience.",
      "Ask clarifying questions to fully understand the issue.",
      "Document the complaint using the 'logCustomerComplaint' function.",
      "Explain the next steps in resolving their issue and provide a timeline if possible."
    ],
    "examples": [
      "I'm truly sorry to hear about your experience. I understand how frustrating that must be.",
      "Let me make sure I understand correctly: you received the wrong item in your order, and when you called previously, no one followed up as promised. Is that correct?",
      "I've documented your complaint (reference #COM-12345). Here's what will happen next: our customer resolution team will review this within 24 hours and contact you at the number we have on file."
    ],
    "transitions": [
      {
        "next_step": "19_immediate_resolution",
        "condition": "If the complaint can be resolved immediately."
      },
      {
        "next_step": "17_specialized_department",
        "condition": "If the complaint requires specialized assistance."
      },
      {
        "next_step": "15_additional_assistance",
        "condition": "If the caller has other issues to discuss after the complaint is logged."
      },
      {
        "next_step": "16_call_conclusion",
        "condition": "If the complaint has been documented and next steps explained."
      }
    ]
  },
  
  {
    "id": "19_immediate_resolution",
    "description": "Provide immediate resolution to the caller's issue when possible.",
    "instructions": [
      "Determine if you have the authority and tools to resolve the issue immediately.",
      "Explain the solution you're proposing and confirm it meets their needs.",
      "Execute the resolution using the appropriate function (e.g., 'issueRefund', 'replaceOrder').",
      "Confirm the resolution has been processed and provide any relevant confirmation details."
    ],
    "examples": [
      "I can definitely help resolve this right away. I'll process a full refund of $67.89 to your original payment method. It should appear in your account within 3-5 business days.",
      "I've arranged for a replacement order to be shipped with expedited delivery at no extra cost. You should receive it by Thursday. The tracking number is TRK123456789."
    ],
    "transitions": [
      {
        "next_step": "15_additional_assistance",
        "condition": "After resolving the immediate issue, check if they need other assistance."
      },
      {
        "next_step": "16_call_conclusion",
        "condition": "If the issue is resolved and the caller is satisfied."
      }
    ]
  },
  
  {
    "id": "20_product_information",
    "description": "Provide detailed information about products or services.",
    "instructions": [
      "Ask which specific product or service they're interested in learning about.",
      "Retrieve and share detailed information using the 'getProductInfo' function.",
      "Cover key aspects like features, pricing, availability, and compatibility.",
      "Offer to answer any specific questions they have about the product."
    ],
    "examples": [
      "Which of our products would you like to know more about today?",
      "Our Premium Plan includes unlimited access to all features, priority customer support, and monthly analytics reports. It's priced at $29.99 per month, with a 20% discount if billed annually. Would you like me to explain any specific feature in more detail?"
    ],
    "transitions": [
      {
        "next_step": "21_product_comparison",
        "condition": "If the caller wants to compare multiple products."
      },
      {
        "next_step": "22_purchase_assistance",
        "condition": "If the caller expresses interest in purchasing."
      },
      {
        "next_step": "15_additional_assistance",
        "condition": "If the caller needs other information."
      },
      {
        "next_step": "16_call_conclusion",
        "condition": "If the caller has all the information they need."
      }
    ]
  },
  
  {
    "id": "21_product_comparison",
    "description": "Help the caller compare different products or service plans.",
    "instructions": [
      "Ask which specific products or plans they want to compare.",
      "Retrieve comparison information using the 'compareProducts' function.",
      "Highlight key differences in features, pricing, and suitability for different needs.",
      "Provide a recommendation based on their stated requirements if appropriate."
    ],
    "examples": [
      "Which specific models would you like to compare today?",
      "When comparing our Basic and Premium plans, the main differences are: Premium offers unlimited storage versus Basic's 10GB limit; Premium includes priority support while Basic has standard support; and Premium costs $29.99 monthly while Basic is $9.99. Based on your needs for extensive storage, I'd recommend considering the Premium plan."
    ],
    "transitions": [
      {
        "next_step": "20_product_information",
        "condition": "If the caller wants more details about a specific product."
      },
      {
        "next_step": "22_purchase_assistance",
        "condition": "If the caller decides to make a purchase."
      },
      {
        "next_step": "15_additional_assistance",
        "condition": "If the caller needs other information."
      },
      {
        "next_step": "16_call_conclusion",
        "condition": "If the caller has all the comparison information they need."
      }
    ]
  },
  
  {
    "id": "22_purchase_assistance",
    "description": "Guide the caller through the purchase process.",
    "instructions": [
      "Confirm which product or service they want to purchase.",
      "Explain the purchase process and payment options.",
      "If they're ready to proceed, collect necessary information for the order.",
      "Process the order using the 'createOrder' function.",
      "Provide order confirmation details and next steps."
    ],
    "examples": [
      "I'd be happy to help you purchase the Premium Plan. Would you like to proceed with monthly billing at $29.99 or annual billing at $287.90, which saves you 20%?",
      "Your order has been successfully processed. Your order number is ORD-12345, and you'll receive a confirmation email shortly. Your new service will be activated within the next 30 minutes."
    ],
    "transitions": [
      {
        "next_step": "20_product_information",
        "condition": "If the caller has more questions before completing the purchase."
      },
      {
        "next_step": "15_additional_assistance",
        "condition": "If the caller needs other assistance after completing the purchase."
      },
      {
        "next_step": "16_call_conclusion",
        "condition": "If the purchase is complete and the caller is satisfied."
      }
    ]
  }
]
</state_machine_example>
</state_machine_info>
