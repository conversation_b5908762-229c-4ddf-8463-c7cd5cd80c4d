@echo off
setlocal enabledelayedexpansion

REM Load configuration
set PORT=3000
for /f "tokens=1,2 delims==" %%a in (config.properties) do (
    if "%%a"=="PORT" set PORT=%%b
)

echo Using port %PORT% from config.properties

REM Check if anything is running on the specified port
set "PID="
for /f "tokens=5" %%a in ('netstat -ano ^| findstr /r ":%PORT% .*LISTENING"') do (
    set PID=%%a
    goto :found_pid
)

:found_pid
if defined PID (
    echo Process with PID %PID% is using port %PORT%. Attempting to terminate...
    taskkill /PID %PID% /F
    timeout /t 2 /nobreak >nul
    echo Port %PORT% is now available.
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules\" (
    echo Installing dependencies...
    call npm install
)

REM Start the application
echo Starting OpenAI Realtime Agents Demo on port %PORT%...
start "" cmd /c "npm run dev -- -p %PORT%"

REM Wait a moment for the server to start
timeout /t 5 /nobreak >nul

REM Open browser
echo Opening browser...
start http://localhost:%PORT%

endlocal
