import { 
  persistentGuardrailCache, 
  persistentSessionCache, 
  persistentCompletionsCache 
} from './persistentCache';

/**
 * A utility class to manage all persistent caches
 */
export class CacheManager {
  private static instance: CacheManager;
  private isShutdownHandlerRegistered = false;

  /**
   * Get the singleton instance of the cache manager
   */
  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    this.registerShutdownHandlers();
  }

  /**
   * Register handlers to save caches on application shutdown
   */
  private registerShutdownHandlers(): void {
    if (this.isShutdownHandlerRegistered) {
      return;
    }

    // Handle normal exit
    process.on('exit', () => {
      console.log('Application exiting, saving caches...');
      this.saveAllCaches();
    });

    // Handle CTRL+C
    process.on('SIGINT', () => {
      console.log('Received SIGINT, saving caches...');
      this.saveAllCaches();
      process.exit(0);
    });

    // Handle kill command
    process.on('SIGTERM', () => {
      console.log('Received SIGTERM, saving caches...');
      this.saveAllCaches();
      process.exit(0);
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('Uncaught exception:', error);
      console.log('Saving caches before exit...');
      this.saveAllCaches();
      process.exit(1);
    });

    this.isShutdownHandlerRegistered = true;
  }

  /**
   * Save all persistent caches to disk
   */
  public saveAllCaches(): void {
    try {
      persistentGuardrailCache.saveToDisk();
      persistentSessionCache.saveToDisk();
      persistentCompletionsCache.saveToDisk();
      console.log('All caches saved successfully');
    } catch (error) {
      console.error('Error saving caches:', error);
    }
  }

  /**
   * Clean up all caches (save and dispose)
   */
  public dispose(): void {
    persistentGuardrailCache.dispose();
    persistentSessionCache.dispose();
    persistentCompletionsCache.dispose();
  }
}

// Initialize the cache manager
export const cacheManager = CacheManager.getInstance();
