"use client";

import React, { useState, useEffect } from "react";
import DetailedCacheViewer from "./DetailedCacheViewer";

// Check if caching is disabled
const CACHING_DISABLED = process.env.NEXT_PUBLIC_DISABLE_CACHING === 'true';

interface CacheStats {
  guardrailCache: { size: number };
  sessionCache: { size: number };
  completionsCache: { size: number };
}

interface CacheEntry {
  value: any;
  timestamp: number;
  formattedDate: string;
  expiresIn: number;
}

type CacheType = "guardrail" | "session" | "completions";

function CacheManager() {
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [viewerOpen, setViewerOpen] = useState(false);
  const [selectedCache, setSelectedCache] = useState<CacheType | null>(null);
  const [cacheContents, setCacheContents] = useState<Record<string, CacheEntry>>({});
  const [loadingContents, setLoadingContents] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"key" | "timestamp" | "expiresIn">("timestamp");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  // Fetch cache statistics
  const fetchCacheStats = async () => {
    try {
      const response = await fetch("/api/cache");
      if (response.ok) {
        const data = await response.json();
        setCacheStats(data);
      } else {
        console.error("Failed to fetch cache stats, response status:", response.status);
        setMessage(`Failed to fetch cache stats: ${response.statusText}`);
      }
    } catch (error) {
      console.error("Error fetching cache stats:", error);
      setMessage("Error: Failed to fetch cache stats. Please ensure your API server is running.");
    }
  };

  // Force save all caches
  const saveAllCaches = async () => {
    setLoading(true);
    setMessage("");

    try {
      const response = await fetch("/api/cache?action=save");

      if (response.ok) {
        const data = await response.json();
        setMessage(data.message);
        // Refresh stats after saving
        fetchCacheStats();
      } else {
        const error = await response.json();
        setMessage(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error("Error saving caches:", error);
      setMessage("Failed to save caches. See console for details.");
    } finally {
      setLoading(false);
    }
  };

  // Reset save attempts counters
  const resetSaveAttempts = async () => {
    setLoading(true);
    setMessage("");

    try {
      const response = await fetch("/api/cache?action=reset-save-attempts");

      if (response.ok) {
        const data = await response.json();
        setMessage(data.message);
        // Refresh stats after resetting
        fetchCacheStats();
      } else {
        const error = await response.json();
        setMessage(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error("Error resetting save attempts:", error);
      setMessage("Failed to reset save attempts. See console for details.");
    } finally {
      setLoading(false);
    }
  };

  // Clear a specific cache or all caches
  const clearCache = async (type: string) => {
    setLoading(true);
    setMessage("");

    try {
      const response = await fetch(`/api/cache?type=${type}`, {
        method: "DELETE",
      });

      if (response.ok) {
        const data = await response.json();
        setMessage(data.message);
        // Refresh stats after clearing
        fetchCacheStats();
        // If the cache viewer is open and showing this cache, refresh it
        if (viewerOpen && selectedCache === type) {
          fetchCacheContents(type as CacheType);
        }
      } else {
        const error = await response.json();
        setMessage(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error("Error clearing cache:", error);
      setMessage("Failed to clear cache. See console for details.");
    } finally {
      setLoading(false);
    }
  };

  // Fetch the contents of a specific cache
  const fetchCacheContents = async (type: CacheType) => {
    console.log(`Fetching cache contents for ${type}`);
    setLoadingContents(true);

    try {
      // Fetch the cache contents without adding test entries
      const response = await fetch(`/api/cache/contents?type=${type}`);
      console.log(`Response status: ${response.status}`);

      if (response.ok) {
        const data = await response.json();
        console.log(`Cache contents:`, data);

        if (Object.keys(data).length === 0) {
          console.warn("No items returned from cache API");

          // Create a dummy entry for testing
          const dummyData = {
            "dummy-key": {
              value: "This is a dummy entry for testing",
              timestamp: Date.now(),
              formattedDate: new Date().toLocaleString(),
              expiresIn: 3600
            }
          };

          setCacheContents(dummyData);
        } else {
          setCacheContents(data);
        }
      } else {
        const error = await response.json();
        console.error(`Error response:`, error);
        setMessage(`Error: ${error.error}`);

        // Create a dummy entry for the error case
        const errorData = {
          "error-key": {
            value: `Error: ${error.error || "Unknown error"}`,
            timestamp: Date.now(),
            formattedDate: new Date().toLocaleString(),
            expiresIn: 3600
          }
        };

        setCacheContents(errorData);
      }
    } catch (error) {
      console.error("Error fetching cache contents:", error);
      setMessage("Failed to fetch cache contents. See console for details.");

      // Create a dummy entry for the exception case
      const exceptionData = {
        "exception-key": {
          value: `Exception: ${error}`,
          timestamp: Date.now(),
          formattedDate: new Date().toLocaleString(),
          expiresIn: 3600
        }
      };

      setCacheContents(exceptionData);
    } finally {
      setLoadingContents(false);
    }
  };

  // Delete a specific cache entry
  const deleteCacheEntry = async (type: CacheType, key: string) => {
    try {
      const encodedKey = encodeURIComponent(key);
      const response = await fetch(`/api/cache/contents?type=${type}&key=${encodedKey}`, {
        method: "DELETE",
      });

      if (response.ok) {
        // Remove the entry from the local state
        const newContents = { ...cacheContents };
        delete newContents[key];
        setCacheContents(newContents);

        // Update the stats
        fetchCacheStats();

        setMessage(`Entry removed from ${type} cache`);
      } else {
        const error = await response.json();
        setMessage(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error("Error deleting cache entry:", error);
      setMessage("Failed to delete cache entry. See console for details.");
    }
  };

  // Open the cache viewer for a specific cache
  const openCacheViewer = (type: CacheType) => {
    console.log(`Opening cache viewer for ${type}`);
    setSelectedCache(type);
    setViewerOpen(true);

    // Use setTimeout to ensure state updates before fetching contents
    setTimeout(() => {
      fetchCacheContents(type);
    }, 100);
  };

  // Close the cache viewer
  const closeCacheViewer = () => {
    setViewerOpen(false);
    setSelectedCache(null);
    setCacheContents({});
    setSearchTerm("");
  };

  // Toggle sort direction or change sort field
  const handleSort = (field: "key" | "timestamp" | "expiresIn") => {
    if (sortBy === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortDirection("desc");
    }
  };

  // Filter and sort cache entries
  const getFilteredAndSortedEntries = () => {
    // Filter entries based on search term
    const filtered = Object.entries(cacheContents).filter(([key]) =>
      key.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Sort entries
    return filtered.sort(([keyA, entryA], [keyB, entryB]) => {
      if (sortBy === "key") {
        return sortDirection === "asc"
          ? keyA.localeCompare(keyB)
          : keyB.localeCompare(keyA);
      } else if (sortBy === "timestamp") {
        return sortDirection === "asc"
          ? entryA.timestamp - entryB.timestamp
          : entryB.timestamp - entryA.timestamp;
      } else { // expiresIn
        return sortDirection === "asc"
          ? entryA.expiresIn - entryB.expiresIn
          : entryB.expiresIn - entryA.expiresIn;
      }
    });
  };

  // Format time remaining until expiration
  const formatTimeRemaining = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  };

  // Fetch stats on initial load
  useEffect(() => {
    fetchCacheStats();

    // Refresh stats every 30 seconds
    const interval = setInterval(fetchCacheStats, 30000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="bg-white rounded-xl p-4 shadow-sm">
      <div className="flex justify-between items-center mb-3">
        <h2 className="text-lg font-semibold">API Cache Manager</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => openCacheViewer("guardrail")}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 font-bold"
          >
            View Cache Contents
          </button>
        </div>
      </div>

      {CACHING_DISABLED && (
        <div className="mb-4 p-3 bg-yellow-100 border border-yellow-300 rounded-md text-yellow-800">
          <div className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
            <span className="font-medium">Caching Disabled</span>
          </div>
          <p className="mt-1 text-sm">
            Caching is currently disabled via the NEXT_PUBLIC_DISABLE_CACHING environment variable.
            No data will be cached or retrieved from cache.
          </p>
        </div>
      )}

      {cacheStats ? (
        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-gray-100 p-3 rounded-lg">
              <div className="font-medium">Guardrail Cache</div>
              <div className="text-xl">{cacheStats.guardrailCache.size} items</div>
              <div className="flex space-x-2 mt-2">
                <button
                  onClick={() => clearCache("guardrail")}
                  disabled={loading}
                  className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50"
                >
                  Clear
                </button>
                <button
                  onClick={() => openCacheViewer("guardrail")}
                  className="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 font-bold"
                >
                  View Contents
                </button>
              </div>
            </div>

            <div className="bg-gray-100 p-3 rounded-lg">
              <div className="font-medium">Session Cache</div>
              <div className="text-xl">{cacheStats.sessionCache.size} items</div>
              <div className="flex space-x-2 mt-2">
                <button
                  onClick={() => clearCache("session")}
                  disabled={loading}
                  className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50"
                >
                  Clear
                </button>
                <button
                  onClick={() => openCacheViewer("session")}
                  className="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 font-bold"
                >
                  View Contents
                </button>
              </div>
            </div>

            <div className="bg-gray-100 p-3 rounded-lg">
              <div className="font-medium">Completions Cache</div>
              <div className="text-xl">{cacheStats.completionsCache.size} items</div>
              <div className="flex space-x-2 mt-2">
                <button
                  onClick={() => clearCache("completions")}
                  disabled={loading}
                  className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50"
                >
                  Clear
                </button>
                <button
                  onClick={() => openCacheViewer("completions")}
                  className="text-xs px-2 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 font-bold"
                >
                  View Contents
                </button>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              <button
                onClick={() => clearCache("all")}
                disabled={loading}
                className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
              >
                Clear All Caches
              </button>

              <button
                onClick={saveAllCaches}
                disabled={loading}
                className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
              >
                Save Caches
              </button>

              <button
                onClick={resetSaveAttempts}
                disabled={loading}
                className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                Reset Save Attempts
              </button>

              <div className="text-xs text-gray-500 mt-2">
                Note: Cache will only save once per session unless reset.
              </div>
            </div>

            <button
              onClick={fetchCacheStats}
              disabled={loading}
              className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
            >
              Refresh Stats
            </button>
          </div>

          {message && (
            <div className="mt-2 p-2 bg-blue-50 text-blue-800 rounded text-sm">
              {message}
            </div>
          )}
        </div>
      ) : (
        <div className="text-gray-500">Loading cache statistics...</div>
      )}

      <div className="mt-4 text-xs text-gray-500">
        <p>Caching reduces API costs by reusing responses for similar requests.</p>
        <p>The guardrail cache stores moderation results, the session cache stores authentication tokens, and the completions cache stores API responses.</p>
      </div>

      {/* Cache Viewer Modal */}
      {viewerOpen && selectedCache && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
          <div className="bg-white rounded-lg shadow-lg w-11/12 max-w-6xl max-h-[90vh] flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-lg font-semibold">
                {selectedCache.charAt(0).toUpperCase() + selectedCache.slice(1)} Cache Contents
              </h3>
              <button
                onClick={closeCacheViewer}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <div className="p-4 border-b">
              <div className="flex items-center space-x-4">
                <div className="flex-grow">
                  <input
                    type="text"
                    placeholder="Search by key..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border rounded-lg"
                  />
                </div>
                <button
                  onClick={() => fetchCacheContents(selectedCache)}
                  className="px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200"
                >
                  Refresh
                </button>
              </div>
            </div>

            <div className="overflow-auto flex-grow">
              {loadingContents ? (
                <div className="p-8 text-center text-gray-500">Loading cache contents...</div>
              ) : Object.keys(cacheContents).length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  <p>No items in cache</p>
                  <button
                    onClick={() => {
                      // Add a test entry to the cache
                      fetch(`/api/cache/test`).then(() => {
                        // Refresh the cache contents with the new test entries
                        // We don't need to add more test entries during the refresh
                        fetchCacheContents(selectedCache);
                      });
                    }}
                    className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    Add Test Entries
                  </button>
                </div>
              ) : (
                <div className="p-4">
                  <div className="mb-6">
                    <h3 className="font-medium text-lg mb-2">Detailed Cache View</h3>
                    <DetailedCacheViewer cacheContents={cacheContents} cacheType={selectedCache} />
                  </div>
                  <h3 className="font-medium text-lg mb-2 mt-8">Cache Entries Table</h3>
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50 sticky top-0">
                      <tr>
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                          onClick={() => handleSort("key")}
                        >
                          <div className="flex items-center">
                            Key
                            {sortBy === "key" && (
                              <span className="ml-1">
                                {sortDirection === "asc" ? "↑" : "↓"}
                              </span>
                            )}
                          </div>
                        </th>
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                          onClick={() => handleSort("timestamp")}
                        >
                          <div className="flex items-center">
                            Last Used
                            {sortBy === "timestamp" && (
                              <span className="ml-1">
                                {sortDirection === "asc" ? "↑" : "↓"}
                              </span>
                            )}
                          </div>
                        </th>
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                          onClick={() => handleSort("expiresIn")}
                        >
                          <div className="flex items-center">
                            Expires In
                            {sortBy === "expiresIn" && (
                              <span className="ml-1">
                                {sortDirection === "asc" ? "↑" : "↓"}
                              </span>
                            )}
                          </div>
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {getFilteredAndSortedEntries().map(([key, entry]) => (
                        <tr key={key} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 max-w-xs truncate">
                            {key}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {entry.formattedDate}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatTimeRemaining(entry.expiresIn)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <button
                              onClick={() => deleteCacheEntry(selectedCache, key)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            <div className="p-4 border-t flex justify-end">
              <button
                onClick={closeCacheViewer}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} // <-- added missing closing brace to complete the component

export default CacheManager;
