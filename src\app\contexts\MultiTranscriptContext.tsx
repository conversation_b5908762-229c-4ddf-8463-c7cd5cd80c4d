"use client";

import React, { createContext, useContext, useState, FC, PropsWithChildren } from "react";
import { v4 as uuidv4 } from "uuid";
import { TranscriptItem } from "@/app/types";

type TranscriptBox = {
  id: string;
  name: string;
  transcriptItems: TranscriptItem[];
  isActive: boolean;
};

type MultiTranscriptContextValue = {
  transcriptBoxes: TranscriptBox[];
  activeBoxId: string;
  addTranscriptBox: (name: string) => string;
  removeTranscriptBox: (id: string) => void;
  setActiveBox: (id: string) => void;
  addTranscriptMessage: (boxId: string, itemId: string, role: "user" | "assistant", text: string, hidden?: boolean) => void;
  updateTranscriptMessage: (boxId: string, itemId: string, text: string, isDelta: boolean) => void;
  addTranscriptBreadcrumb: (boxId: string, title: string, data?: Record<string, any>) => void;
  toggleTranscriptItemExpand: (boxId: string, itemId: string) => void;
  updateTranscriptItem: (boxId: string, itemId: string, updatedProperties: Partial<TranscriptItem>) => void;
  switchToNextBox: () => void;
  getTranscriptItems: (boxId: string) => TranscriptItem[];
  clearTranscriptBox: (boxId: string) => void;
};

const MultiTranscriptContext = createContext<MultiTranscriptContextValue | undefined>(undefined);

export const MultiTranscriptProvider: FC<PropsWithChildren> = ({ children }) => {
  const [transcriptBoxes, setTranscriptBoxes] = useState<TranscriptBox[]>([
    {
      id: "box-1",
      name: "Conversation 1",
      transcriptItems: [],
      isActive: true
    }
  ]);

  const [activeBoxId, setActiveBoxId] = useState<string>("box-1");

  function newTimestampPretty(): string {
    return new Date().toLocaleTimeString([], {
      hour12: true,
      hour: "numeric",
      minute: "2-digit",
      second: "2-digit",
    });
  }

  const addTranscriptBox: MultiTranscriptContextValue["addTranscriptBox"] = (name) => {
    const newBoxId = `box-${uuidv4()}`;
    setTranscriptBoxes((prev) => [
      ...prev,
      {
        id: newBoxId,
        name,
        transcriptItems: [],
        isActive: false
      }
    ]);
    return newBoxId;
  };

  const removeTranscriptBox: MultiTranscriptContextValue["removeTranscriptBox"] = (id) => {
    setTranscriptBoxes((prev) => prev.filter(box => box.id !== id));

    // If we're removing the active box, switch to the first available box
    if (id === activeBoxId) {
      const remainingBoxes = transcriptBoxes.filter(box => box.id !== id);
      if (remainingBoxes.length > 0) {
        setActiveBoxId(remainingBoxes[0].id);
      }
    }
  };

  const setActiveBox: MultiTranscriptContextValue["setActiveBox"] = (id) => {
    setActiveBoxId(id);
    setTranscriptBoxes(prev =>
      prev.map(box => ({
        ...box,
        isActive: box.id === id
      }))
    );
  };

  const addTranscriptMessage: MultiTranscriptContextValue["addTranscriptMessage"] = (boxId, itemId, role, text = "", isHidden = false) => {
    setTranscriptBoxes((prev) => {
      return prev.map(box => {
        if (box.id !== boxId) return box;

        // Check if message already exists in this box
        if (box.transcriptItems.some((log) => log.itemId === itemId && log.type === "MESSAGE")) {
          console.warn(`[addTranscriptMessage] skipping; message already exists for boxId=${boxId}, itemId=${itemId}, role=${role}`);
          return box;
        }

        const newItem: TranscriptItem = {
          itemId,
          type: "MESSAGE",
          role,
          title: text,
          expanded: false,
          timestamp: newTimestampPretty(),
          createdAtMs: Date.now(),
          status: "IN_PROGRESS",
          isHidden,
        };

        return {
          ...box,
          transcriptItems: [...box.transcriptItems, newItem]
        };
      });
    });
  };

  const updateTranscriptMessage: MultiTranscriptContextValue["updateTranscriptMessage"] = (boxId, itemId, newText, append = false) => {
    setTranscriptBoxes((prev) => {
      return prev.map(box => {
        if (box.id !== boxId) return box;

        return {
          ...box,
          transcriptItems: box.transcriptItems.map((item) => {
            if (item.itemId === itemId && item.type === "MESSAGE") {
              return {
                ...item,
                title: append ? (item.title ?? "") + newText : newText,
              };
            }
            return item;
          })
        };
      });
    });
  };

  const addTranscriptBreadcrumb: MultiTranscriptContextValue["addTranscriptBreadcrumb"] = (boxId, title, data) => {
    setTranscriptBoxes((prev) => {
      return prev.map(box => {
        if (box.id !== boxId) return box;

        return {
          ...box,
          transcriptItems: [
            ...box.transcriptItems,
            {
              itemId: `breadcrumb-${uuidv4()}`,
              type: "BREADCRUMB",
              title,
              data,
              expanded: false,
              timestamp: newTimestampPretty(),
              createdAtMs: Date.now(),
              status: "DONE",
              isHidden: false,
            }
          ]
        };
      });
    });
  };

  const toggleTranscriptItemExpand: MultiTranscriptContextValue["toggleTranscriptItemExpand"] = (boxId, itemId) => {
    setTranscriptBoxes((prev) => {
      return prev.map(box => {
        if (box.id !== boxId) return box;

        return {
          ...box,
          transcriptItems: box.transcriptItems.map((log) =>
            log.itemId === itemId ? { ...log, expanded: !log.expanded } : log
          )
        };
      });
    });
  };

  const updateTranscriptItem: MultiTranscriptContextValue["updateTranscriptItem"] = (boxId, itemId, updatedProperties) => {
    setTranscriptBoxes((prev) => {
      return prev.map(box => {
        if (box.id !== boxId) return box;

        return {
          ...box,
          transcriptItems: box.transcriptItems.map((item) =>
            item.itemId === itemId ? { ...item, ...updatedProperties } : item
          )
        };
      });
    });
  };

  const switchToNextBox: MultiTranscriptContextValue["switchToNextBox"] = () => {
    const currentIndex = transcriptBoxes.findIndex(box => box.id === activeBoxId);
    const nextIndex = (currentIndex + 1) % transcriptBoxes.length;
    setActiveBox(transcriptBoxes[nextIndex].id);
  };

  const getTranscriptItems: MultiTranscriptContextValue["getTranscriptItems"] = (boxId) => {
    const box = transcriptBoxes.find(box => box.id === boxId);
    return box ? box.transcriptItems : [];
  };

  const clearTranscriptBox: MultiTranscriptContextValue["clearTranscriptBox"] = (boxId) => {
    setTranscriptBoxes((prev) => {
      return prev.map(box => {
        if (box.id !== boxId) return box;

        return {
          ...box,
          transcriptItems: []
        };
      });
    });
  };

  return (
    <MultiTranscriptContext.Provider
      value={{
        transcriptBoxes,
        activeBoxId,
        addTranscriptBox,
        removeTranscriptBox,
        setActiveBox,
        addTranscriptMessage,
        updateTranscriptMessage,
        addTranscriptBreadcrumb,
        toggleTranscriptItemExpand,
        updateTranscriptItem,
        switchToNextBox,
        getTranscriptItems,
        clearTranscriptBox
      }}
    >
      {children}
    </MultiTranscriptContext.Provider>
  );
};

export function useMultiTranscript() {
  const context = useContext(MultiTranscriptContext);
  if (!context) {
    throw new Error("useMultiTranscript must be used within a MultiTranscriptProvider");
  }
  return context;
}
