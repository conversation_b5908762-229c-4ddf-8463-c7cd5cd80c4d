import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This middleware ensures the application only serves content in English
export function middleware(request: NextRequest) {
  // Get the accept-language header
  const acceptLanguage = request.headers.get('accept-language') || '';
  
  // Set the Content-Language header to English
  const response = NextResponse.next();
  response.headers.set('Content-Language', 'en');
  
  return response;
}

// Configure the middleware to run on all routes
export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
