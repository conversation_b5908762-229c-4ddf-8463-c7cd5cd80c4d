"use client";

import React from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface CodeBlockProps {
  language: string;
  value: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ language, value }) => {
  // Normalize language name
  const normalizedLanguage = normalizeLanguage(language);

  return (
    <div className="rounded-md overflow-hidden my-2">
      <div className="bg-gray-800 text-gray-200 px-4 py-1 text-xs font-mono flex justify-between items-center">
        <span>{normalizedLanguage}</span>
        <button
          onClick={() => copyToClipboard(value)}
          className="text-gray-400 hover:text-white transition-colors"
        >
          Copy
        </button>
      </div>
      <SyntaxHighlighter
        language={normalizedLanguage}
        style={vscDarkPlus}
        customStyle={{
          margin: 0,
          padding: '0.75rem',
          fontSize: '0.9rem',
          lineHeight: '1.3',
        }}
      >
        {value}
      </SyntaxHighlighter>
    </div>
  );
};

// Helper function to copy code to clipboard
const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text)
    .then(() => {
      // Could add a toast notification here
      console.log('Code copied to clipboard');
    })
    .catch(err => {
      console.error('Failed to copy code: ', err);
    });
};

// Helper function to normalize language names
const normalizeLanguage = (language: string): string => {
  if (!language) return 'text';

  // Map common language names to their syntax highlighter equivalents
  const languageMap: Record<string, string> = {
    'js': 'javascript',
    'jsx': 'jsx',
    'ts': 'typescript',
    'tsx': 'tsx',
    'py': 'python',
    'rb': 'ruby',
    'java': 'java',
    'go': 'go',
    'rust': 'rust',
    'c': 'c',
    'cpp': 'cpp',
    'cs': 'csharp',
    'php': 'php',
    'html': 'html',
    'css': 'css',
    'json': 'json',
    'yml': 'yaml',
    'yaml': 'yaml',
    'md': 'markdown',
    'sh': 'bash',
    'bash': 'bash',
    'shell': 'bash',
    'sql': 'sql',
    'diff': 'diff',
  };

  return languageMap[language.toLowerCase()] || language.toLowerCase();
};

export default CodeBlock;
