import { AgentConfig } from "@/app/types";

const highPerformanceJavaExpert: AgentConfig = {
  name: "highPerformanceJavaExpert",
  publicDescription: "Ultra-High-Performance Java Expert specializing in extreme optimization techniques for latency-critical and high-throughput Java applications, including deep expertise in Spring Framework performance tuning.",
  instructions: `
# Personality and Tone
You are a Java Performance Optimization Guru with 20+ years of experience tuning mission-critical Java applications, including large-scale Spring and Spring Boot systems. You've optimized systems for NASA, CERN, major stock exchanges, and high-frequency trading firms where nanoseconds matter. Your communication style is direct, data-driven, and focused on extreme performance gains. You're passionate about squeezing every bit of performance from hardware and software.

# Answering Style
**Answer performance questions with these components in a compact format without skipping lines:**
1. **Immediate Solution:** Start with the most effective optimization technique for the specific scenario (including Spring-specific best practices)
2. **Performance Impact:** Quantify the expected improvement with precise metrics (e.g., "This reduces p99 latency by 42-67% and increases throughput by 3.2x")
3. **Implementation Details:** Provide specific code examples showing before/after optimization with detailed comments, including Spring idioms. Always use proper syntax highlighting by specifying the language in code blocks.
4. **Measurement Approach:** Explain exactly how to benchmark and verify the improvement using JMH, async-profiler, or Spring Boot Actuator metrics
5. **Trade-offs:** Discuss any drawbacks or considerations of the optimization with quantified impacts
6. **Advanced Alternative:** Always suggest an even more aggressive optimization approach for extreme cases

Group all these components together in a compact format without unnecessary line breaks. Use minimal spacing between sections and avoid double line breaks. Keep your responses concise and to the point. When explaining concepts, always provide code examples with proper syntax highlighting. **Bold all technical keywords** throughout your response instead of listing them separately at the beginning. Format your answers to maximize information density while maintaining readability.

# Core Expertise Areas
- JVM tuning and garbage collection elimination
- Memory management and zero-allocation techniques
- Lock-free and wait-free concurrent programming
- Spring Framework and Spring Boot performance tuning (context startup, bean lifecycle, dependency injection, AOP, transaction management, WebMVC, WebFlux, Data, Security, etc.)
- Spring container optimizations (lazy initialization, bean scope, conditional beans)
- Spring Boot Actuator and Micrometer metrics for real-time monitoring
- Spring Data JPA/Hibernate tuning (fetch strategies, batching, query optimization)
- Spring Transaction management and connection pool tuning
- Spring WebMVC vs WebFlux performance trade-offs
- Reactive programming with Project Reactor and WebFlux
- Custom data structures optimized for specific access patterns
- Database access optimization and custom drivers
- Zero-copy network I/O and kernel bypass techniques
- Profiling and performance testing at nanosecond scale
- Distributed systems performance with minimal coordination
- Microservices performance patterns and data locality
- Cloud-native Java optimization with resource-aware scheduling
- JNI and native code integration for performance-critical sections
- Ahead-of-Time compilation and GraalVM native image optimization
- React.js optimization and performance tuning (component lifecycle, memo, useMemo, useCallback, code splitting, lazy loading)
- Docker containerization best practices (multi-stage builds, layer optimization, image size reduction)
- Kubernetes deployment optimization (resource limits, HPA, pod affinity, node selection)
- Git workflow optimization (monorepo strategies, git hooks, branching models)

# Ultra-High-Performance Java & Spring Patterns
Be ready to explain and provide code examples for:
- Thread-per-core architecture with affinity pinning
- Non-blocking I/O with direct memory access
- Memory-mapped files with huge pages
- Off-heap memory management with sun.misc.Unsafe
- Lock-free and wait-free algorithms using atomics
- Mechanical sympathy techniques for CPU cache optimization
- JVM JIT optimization techniques and profile-guided optimization
- Specialized collections (HPPC, FastUtil, Eclipse Collections, Agrona)
- Data locality optimization with struct-of-arrays layout
- Cache-friendly algorithms with prefetching hints
- Zero-copy techniques with direct ByteBuffers
- Vectorization and SIMD in Java with JDK Vector API
- Custom thread scheduling with priority inversion avoidance
- Kernel bypass networking with technologies like DPDK
- Busy-spinning techniques for ultra-low latency
- Value types and inline classes (JDK preview features)
- Compiler intrinsics and JVM flags for performance
- GC-less programming techniques
- Aeron and other ultra-low-latency messaging systems
- Chronicle Queue and other persistence techniques
- LMAX Disruptor pattern for high-throughput sequencing
- **Spring-specific patterns:** Lazy bean initialization, conditional beans, @Profile, @Async, @Transactional tuning, custom scopes, actuator metrics, WebFlux vs WebMVC, Data JPA fetch tuning, connection pool sizing, startup time reduction, GraalVM native-image for Spring Boot, etc.
- **React.js optimization patterns:**
  - Virtual DOM optimization techniques
  - React.memo for component memoization
  - useCallback for function memoization
  - useMemo for expensive computations
  - Code splitting and lazy loading
  - Server-side rendering optimization
  - Bundle size optimization
  - React DevTools performance profiling
- **Docker optimization patterns:**
  - Multi-stage builds for minimal image size
  - Layer caching strategies
  - JVM container-aware configuration
  - Custom base images for Java applications
  - Resource constraint tuning
  - Build context optimization
- **Kubernetes deployment patterns:**
  - Horizontal pod autoscaling
  - Resource quota management
  - Node affinity and anti-affinity
  - Pod topology spread constraints
  - Init containers for setup
  - Sidecar patterns for logging/monitoring
  - StatefulSet vs Deployment strategies
- **Git workflow patterns:**
  - Monorepo management strategies
  - Git hooks for performance checks
  - Branch management automation
  - CI/CD pipeline optimization
  - Git LFS for large files
  - Submodule optimization
`,
  tools: [
    {
      type: "function",
      name: "analyzeJavaPerformance",
      description: "Analyzes Java code for performance issues and provides optimization recommendations.",
      parameters: {
        type: "object",
        properties: {
          code: {
            type: "string",
            description: "The Java code to analyze for performance issues."
          },
          context: {
            type: "string",
            description: "The context in which this code runs (e.g., 'high-frequency trading', 'web service', 'batch processing')."
          }
        },
        required: ["code"]
      }
    },
    {
      type: "function",
      name: "benchmarkJavaCode",
      description: "Provides JMH benchmark code for measuring the performance of Java implementations.",
      parameters: {
        type: "object",
        properties: {
          implementation: {
            type: "string",
            description: "Description of the implementation to benchmark (e.g., 'ArrayList vs LinkedList', 'synchronized vs ReentrantLock')."
          },
          operationType: {
            type: "string",
            enum: ["read", "write", "mixed"],
            description: "The primary operation type to benchmark."
          }
        },
        required: ["implementation"]
      }
    },
    {
      type: "function",
      name: "getJVMTuningParameters",
      description: "Provides recommended JVM tuning parameters for specific use cases.",
      parameters: {
        type: "object",
        properties: {
          useCase: {
            type: "string",
            description: "The use case for JVM tuning (e.g., 'low-latency trading', 'high-throughput processing', 'microservices')."
          },
          memoryConstraints: {
            type: "string",
            description: "Memory constraints for the application (e.g., '4GB heap', '16GB container')."
          },
          gcPreference: {
            type: "string",
            enum: ["throughput", "latency", "footprint", "balanced"],
            description: "The garbage collection optimization preference."
          }
        },
        required: ["useCase"]
      }
    },
    {
      type: "function",
      name: "analyzeReactPerformance",
      description: "Analyzes React.js code for performance issues and provides optimization recommendations.",
      parameters: {
        type: "object",
        properties: {
          code: {
            type: "string",
            description: "The React.js code to analyze for performance issues."
          },
          context: {
            type: "string",
            description: "The context in which this code runs (e.g., 'high-traffic website', 'data-intensive dashboard')."
          }
        },
        required: ["code"]
      }
    },
    {
      type: "function",
      name: "analyzeDockerConfig",
      description: "Analyzes Dockerfile and docker-compose configurations for optimization opportunities.",
      parameters: {
        type: "object",
        properties: {
          config: {
            type: "string",
            description: "The Dockerfile or docker-compose.yml content to analyze."
          },
          type: {
            type: "string",
            enum: ["dockerfile", "compose"],
            description: "The type of configuration being analyzed."
          }
        },
        required: ["config", "type"]
      }
    },
    {
      type: "function",
      name: "analyzeKubernetesConfig",
      description: "Analyzes Kubernetes manifests for optimization and best practices.",
      parameters: {
        type: "object",
        properties: {
          manifest: {
            type: "string",
            description: "The Kubernetes manifest content to analyze."
          },
          resourceType: {
            type: "string",
            enum: ["deployment", "statefulset", "service", "ingress", "configmap", "secret", "other"],
            description: "The type of Kubernetes resource being analyzed."
          }
        },
        required: ["manifest"]
      }
    },
    {
      type: "function",
      name: "analyzeGitWorkflow",
      description: "Analyzes Git workflow patterns and repository structure for optimization.",
      parameters: {
        type: "object",
        properties: {
          repoStats: {
            type: "object",
            properties: {
              branchCount: { type: "number" },
              commitCount: { type: "number" },
              largeFiles: { type: "array", items: { type: "string" } },
              hookConfigs: { type: "array", items: { type: "string" } }
            }
          }
        },
        required: ["repoStats"]
      }
    }
  ],
  toolLogic: {

    analyzeJavaPerformance: ({ code, context = "general" }) => {
      // Early return for empty or very short code
      if (!code || code.length < 10) {
        return {
          issues: [],
          summary: "Code sample too short for analysis",
          generalRecommendations: []
        };
      }

      // Create a cache for memoization
      const analyzeJavaPerformanceCache = new Map();

      // Check cache first for identical code and context
      const cacheKey = `${code.substring(0, 100)}|${context}`;
      if (analyzeJavaPerformanceCache.has(cacheKey)) {
        console.log("Cache hit for performance analysis");
        return analyzeJavaPerformanceCache.get(cacheKey);
      }

      // Ultra-fast performance analysis with advanced pattern matching
      const performanceIssues = [];

      // Pre-compiled regex patterns for faster matching
      const patterns = {
        autowired: /@Autowired\s+(\w+|private)/,
        transactional: /@Transactional\s*\n\s*public/,
        restController: /@RestController/,
        publicList: /public\s+List</,
        getMapping: /@GetMapping/,
        stringConcat: /\+.*toString\(\)/,
        collectionInit: /new ArrayList<>\(\)/,
        synchronization: /synchronized/,
        primitiveBoxing: /(Integer|Long|Double).*List<|Map<|Set</,
        springAnnotations: /@(Component|Service|Repository|Controller)/,
        collections: /List<|Set<|Map</
      };

      // Single-pass code analysis to avoid multiple regex tests
      const hasAutowired = patterns.autowired.test(code);
      const hasTransactional = patterns.transactional.test(code);
      const hasRestController = patterns.restController.test(code) &&
                               patterns.publicList.test(code) &&
                               patterns.getMapping.test(code);
      const hasStringConcat = patterns.stringConcat.test(code);
      const hasCollectionInit = patterns.collectionInit.test(code);
      const hasSynchronization = patterns.synchronization.test(code);
      const hasPrimitiveBoxing = patterns.primitiveBoxing.test(code);
      const hasSpringAnnotations = patterns.springAnnotations.test(code);
      const hasCollections = patterns.collections.test(code);

      // High performance contexts check
      const highPerfContexts = new Set(['high-frequency', 'low-latency', 'trading']);
      const isHighPerfContext = context &&
        [...highPerfContexts].some(ctx => context.includes(ctx));

      // Spring-specific performance issues - using pre-compiled patterns
      if (patterns.autowired.test(code)) {
        performanceIssues.push({
          issue: "Field injection with @Autowired",
          impact: "Medium - Reduces testability and can slow context startup",
          recommendation: "Use constructor injection for better performance and maintainability",
          before: `@Autowired\nprivate MyService myService;`,
          after: `private final MyService myService;\n\n@Autowired\npublic MyClass(MyService myService) {\n    this.myService = myService;\n}`,
          explanation: "Constructor injection is preferred in Spring for immutability, easier testing, and faster context startup."
        });
      }
      if (patterns.transactional.test(code)) {
        performanceIssues.push({
          issue: "Excessive use of @Transactional at method level",
          impact: "Medium - Can cause unnecessary proxying and transaction boundaries",
          recommendation: "Apply @Transactional at the class level or use fine-grained boundaries only where needed",
          before: `@Transactional\npublic void doWork() { ... }`,
          after: `@Transactional\npublic class MyService {\n    public void doWork() { ... }\n}`,
          explanation: "Class-level @Transactional reduces proxy overhead and improves transaction demarcation."
        });
      }
      if (code.includes("DataSourceBuilder.create()") || code.includes("HikariDataSource") && code.includes("setMaximumPoolSize(10)")) {
        performanceIssues.push({
          issue: "Default or low connection pool size in Spring",
          impact: "High - Limits throughput under load",
          recommendation: "Tune HikariCP pool size based on CPU and workload",
          before: `HikariDataSource ds = new HikariDataSource();\nds.setMaximumPoolSize(10);`,
          after: `HikariDataSource ds = new HikariDataSource();\nds.setMaximumPoolSize(Runtime.getRuntime().availableProcessors() * 2);`,
          explanation: "A too-small pool size can throttle throughput. Tune based on concurrency and database capacity."
        });
      }
      if (patterns.restController.test(code) && patterns.publicList.test(code) && patterns.getMapping.test(code)) {
        performanceIssues.push({
          issue: "Synchronous controller in high-throughput Spring app",
          impact: "Critical - Wastes threads and limits scalability",
          recommendation: "Use reactive programming with WebFlux (Flux/Mono) for non-blocking I/O",
          before: `@RestController\n@GetMapping(\"/items\")\npublic List<Item> getItems() { ... }`,
          after: `@RestController\n@GetMapping(\"/items\")\npublic Flux<Item> getItems() { ... }`,
          explanation: "WebFlux enables handling 10-100x more concurrent requests by eliminating thread blocking."
        });
      }
      if (code.includes("@SpringBootApplication") && code.includes("@ComponentScan")) {
        performanceIssues.push({
          issue: "Large component scan in Spring Boot",
          impact: "Medium - Slows down context startup",
          recommendation: "Limit @ComponentScan to specific packages",
          before: `@SpringBootApplication\n@ComponentScan`,
          after: `@SpringBootApplication\n@ComponentScan(\"com.example.important\")`,
          explanation: "Narrowing the scan scope reduces startup time and memory usage."
        });
      }
      if (code.includes("@Entity") && code.includes("fetch = FetchType.EAGER")) {
        performanceIssues.push({
          issue: "Eager fetching in JPA entities",
          impact: "High - Causes unnecessary data loading and memory usage",
          recommendation: "Use lazy fetching and fetch joins only when needed",
          before: `@OneToMany(fetch = FetchType.EAGER)`,
          after: `@OneToMany(fetch = FetchType.LAZY)`,
          explanation: "Lazy fetching avoids loading large object graphs unless required."
        });
      }
      if (code.includes("@Bean") && code.includes("@Lazy")) {
        performanceIssues.push({
          issue: "Eager bean initialization",
          impact: "Medium - Increases Spring context startup time",
          recommendation: "Use @Lazy for non-critical beans",
          before: `@Bean\npublic MyBean myBean() { ... }`,
          after: `@Bean\n@Lazy\npublic MyBean myBean() { ... }`,
          explanation: "@Lazy defers bean creation until needed, reducing startup time."
        });
      }

      // Collection initialization optimizations
      if (patterns.collectionInit.test(code)) {
        performanceIssues.push({
          issue: "Uninitialized ArrayList capacity",
          impact: "Medium - Causes multiple resizing operations and memory copies (up to 233% overhead)",
          recommendation: "Initialize ArrayList with expected capacity",
          before: "List<String> items = new ArrayList<>();",
          after: "List<String> items = new ArrayList<>(expectedSize);",
          explanation: "Pre-sizing collections avoids expensive resize operations that copy the entire backing array. Each resize typically doubles capacity and requires a full array copy.",
          advancedAlternative: "For maximum performance, consider specialized collections like Eclipse Collections' FastList, HPPC's ObjectArrayList, or Agrona's ArrayListUtil that offer better memory layout and faster operations."
        });
      }

      // Synchronization optimizations
      if (patterns.synchronization.test(code)) {
        performanceIssues.push({
          issue: "Synchronized blocks or methods",
          impact: "Critical - Creates contention points and limits scalability (5-20x slower under contention)",
          recommendation: "Use java.util.concurrent classes or lock-free algorithms",
          before: `
public synchronized void addItem(String item) {
    items.add(item);
}`,
          after: `
// Option 1: Fine-grained locking with ReentrantLock
private final Lock lock = new ReentrantLock();
public void addItem(String item) {
    lock.lock();
    try {
        items.add(item);
    } finally {
        lock.unlock();
    }
}

// Option 2: Lock-free with concurrent collections
private final ConcurrentLinkedQueue<String> items = new ConcurrentLinkedQueue<>();
public void addItem(String item) {
    items.add(item);
}`,
          explanation: "ReentrantLock provides 30-50% better performance than synchronized for high-contention scenarios, and concurrent collections can eliminate locking entirely for many use cases.",
          advancedAlternative: `
// Ultra-high performance with LMAX Disruptor pattern
private final RingBuffer<ItemEvent> ringBuffer;

// Producer code
public void addItem(String item) {
    long sequence = ringBuffer.next();
    try {
        ItemEvent event = ringBuffer.get(sequence);
        event.setItem(item);
    } finally {
        ringBuffer.publish(sequence);
    }
}

// Consumer code in separate thread
public void handleEvents() {
    disruptor.handleEventsWith((event, sequence, endOfBatch) -> {
        processItem(event.getItem());
    });
}`
        });
      }

      // String handling optimizations
      if (code.includes(".toString()") && code.includes("+") && !code.includes("StringBuilder")) {
        performanceIssues.push({
          issue: "String concatenation in loops",
          impact: "High - Creates many temporary String objects (up to 500% memory overhead and GC pressure)",
          recommendation: "Use StringBuilder with pre-sized capacity",
          before: `
String result = "";
for (int i = 0; i < items.size(); i++) {
    result += items.get(i) + ", ";
}`,
          after: `
// Pre-size with precise capacity estimate to avoid any resizing
StringBuilder result = new StringBuilder(items.size() *
    (avgItemLength + 2)); // +2 for ", " separator
for (int i = 0; i < items.size(); i++) {
    result.append(items.get(i)).append(", ");
}
String finalResult = result.toString();`,
          explanation: "StringBuilder with proper pre-sizing avoids both intermediate String objects and internal buffer resizing, reducing GC pressure by up to 95% and improving throughput by 3-5x for string-heavy operations.",
          advancedAlternative: `
// For extreme performance, use direct byte manipulation
byte[] buffer = new byte[estimatedSize];
int position = 0;
for (int i = 0; i < items.size(); i++) {
    String item = items.get(i);
    // Copy bytes directly
    System.arraycopy(item.getBytes(), 0, buffer, position, item.length());
    position += item.length();

    // Add separator
    buffer[position++] = ',';
    buffer[position++] = ' ';
}
// Create final string only once
String finalResult = new String(buffer, 0, position > 0 ? position - 2 : 0);`
        });
      }

      // Collection iteration optimizations
      if (code.includes("for (") && code.includes(".size()") && code.includes(".get(")) {
        performanceIssues.push({
          issue: "Inefficient collection iteration",
          impact: "Medium - Repeated size() calls and bounds checking (15-30% overhead)",
          recommendation: "Use optimized iteration patterns",
          before: `
for (int i = 0; i < list.size(); i++) {
    process(list.get(i));
}`,
          after: `
// Option 1: Cache size and use array-style indexing
final int size = list.size(); // final for JIT optimization hints
for (int i = 0; i < size; i++) {
    process(list.get(i));
}

// Option 2: Enhanced for-loop with JIT optimizations
for (Item item : list) {
    process(item);
}

// Option 3: Specialized iteration for ArrayList
if (list instanceof ArrayList) {
    ArrayList<Item> arrayList = (ArrayList<Item>) list;
    arrayList.forEach(this::process); // Uses optimized internal iteration
}`,
          explanation: "Caching the size eliminates repeated method calls, and enhanced for-loops enable JVM escape analysis and other optimizations. ArrayList.forEach() uses internal optimized iteration that avoids virtual method calls.",
          advancedAlternative: `
// For maximum performance with ArrayList, use direct array access
if (list instanceof ArrayList) {
    // Warning: Uses reflection to access internal array - fragile but fast
    try {
        Field elementDataField = ArrayList.class.getDeclaredField("elementData");
        elementDataField.setAccessible(true);
        Object[] elements = (Object[]) elementDataField.get(list);
        int size = list.size();
        for (int i = 0; i < size; i++) {
            @SuppressWarnings("unchecked")
            Item item = (Item) elements[i];
            process(item);
        }
    } catch (Exception e) {
        // Fallback to normal iteration
        for (Item item : list) {
            process(item);
        }
    }
}`
        });
      }

      // Boxed primitives optimization
      if (patterns.primitiveBoxing.test(code)) {
        performanceIssues.push({
          issue: "Boxed primitives in collections",
          impact: "High - Causes excessive memory usage and GC pressure (5-10x overhead)",
          recommendation: "Use specialized primitive collections",
          before: `
List<Integer> values = new ArrayList<>();
for (int i = 0; i < 1000000; i++) {
    values.add(i);
}
int sum = 0;
for (Integer value : values) {
    sum += value;
}`,
          after: `
// Option 1: Trove library
TIntArrayList values = new TIntArrayList(1000000);
for (int i = 0; i < 1000000; i++) {
    values.add(i);
}
int sum = 0;
values.forEach(value -> sum += value);

// Option 2: Eclipse Collections
MutableIntList values = IntLists.mutable.withInitialCapacity(1000000);
for (int i = 0; i < 1000000; i++) {
    values.add(i);
}
int sum = values.sum();`,
          explanation: "Primitive collections eliminate boxing/unboxing overhead and reduce memory usage by up to 80%. This significantly reduces GC pressure and improves cache locality.",
          advancedAlternative: `
// For extreme performance, use direct arrays with manual management
int[] values = new int[1000000];
for (int i = 0; i < values.length; i++) {
    values[i] = i;
}
int sum = 0;
for (int i = 0; i < values.length; i++) {
    sum += values[i];
}

// Or use JDK 16+ Vector API for SIMD operations
IntVector sumVector = IntVector.zero(SPECIES_PREFERRED);
for (int i = 0; i < values.length; i += SPECIES_PREFERRED.length()) {
    sumVector = sumVector.add(IntVector.fromArray(SPECIES_PREFERRED, values, i));
}
int sum = sumVector.reduceLanes(VectorOperators.ADD);`
        });
      }

      // Stream API misuse
      if (code.includes(".stream()") && code.includes(".collect(") &&
          !code.includes("parallel") && !code.includes("filter(") && !code.includes("map(")) {
        performanceIssues.push({
          issue: "Unnecessary Stream API usage",
          impact: "Medium - Adds overhead for simple operations (30-100% slower)",
          recommendation: "Use direct collection operations for simple cases",
          before: `
List<String> filtered = items.stream()
    .collect(Collectors.toList());`,
          after: `
// For simple copying, use constructor or addAll
List<String> filtered = new ArrayList<>(items);

// Or if you need a specific implementation
List<String> filtered = new LinkedList<>();
filtered.addAll(items);`,
          explanation: "Stream API adds overhead for simple operations. For basic collection transformations without intermediate operations, direct collection methods are 1.5-2x faster.",
          advancedAlternative: `
// For maximum performance with specific collection types
if (items instanceof ArrayList && filtered instanceof ArrayList) {
    ArrayList<String> source = (ArrayList<String>) items;
    ArrayList<String> target = (ArrayList<String>) filtered;
    target.ensureCapacity(source.size());
    for (int i = 0; i < source.size(); i++) {
        target.add(source.get(i));
    }
}`
        });
      }

      // Context-specific optimizations for high-frequency trading - using O(1) lookup
      if (isHighPerfContext) {
        performanceIssues.push({
          issue: "Allocation in critical path",
          impact: "Critical - Causes GC pressure and unpredictable latency spikes (p99.9 latency impact of 10-100ms)",
          recommendation: "Use zero-allocation techniques",
          before: `
public TradeResult processTrade(TradeRequest request) {
    TradeValidationResult validation = new TradeValidationResult();
    // Process trade
    return new TradeResult(validation, calculateFees(request));
}`,
          after: `
// Option 1: Object pooling with thread-local caches
private static final ThreadLocal<ObjectPool<TradeValidationResult>> validationPoolTL =
    ThreadLocal.withInitial(() -> new ObjectPool<>(TradeValidationResult::new, 100));
private static final ThreadLocal<ObjectPool<TradeResult>> resultPoolTL =
    ThreadLocal.withInitial(() -> new ObjectPool<>(TradeResult::new, 100));

public TradeResult processTrade(TradeRequest request) {
    ObjectPool<TradeValidationResult> validationPool = validationPoolTL.get();
    ObjectPool<TradeResult> resultPool = resultPoolTL.get();

    TradeValidationResult validation = validationPool.borrow();
    validation.reset(); // Clear previous state

    // Process trade with zero allocations
    validation.validate(request);

    TradeResult result = resultPool.borrow();
    result.reset(); // Clear previous state
    result.set(validation, calculateFeesNoAlloc(request));

    // Register cleanup callback to return objects to pool when done
    result.setReleaseCallback(() -> {
        validationPool.release(validation);
        resultPool.release(result);
    });

    return result;
}`,
          explanation: "Thread-local object pooling eliminates allocations in critical paths, reducing GC pressure by 95-99% and improving p99.9 latency by 10-50x in latency-sensitive applications.",
          advancedAlternative: `
// Option 2: Value types with struct-like memory layout
// Using JDK 15+ inline types preview feature or JDK 17+ primitive classes
// Or libraries like jOOQ's RecordImpl or Agrona's DirectBuffer

// Using Agrona for zero-allocation, cache-friendly processing
private final UnsafeBuffer tradeBuffer = new UnsafeBuffer(new byte[8192]);
private final TradeEncoder encoder = new TradeEncoder();
private final TradeDecoder decoder = new TradeDecoder();

public void processTrade(DirectBuffer requestBuffer, int offset, int length,
                        MutableDirectBuffer resultBuffer) {
    // Decode request directly from memory without allocation
    decoder.wrap(requestBuffer, offset, length);

    // Process with zero allocations
    final int resultOffset = 0;
    encoder.wrap(resultBuffer, resultOffset);
    encoder.tradeId(decoder.tradeId());
    encoder.price(calculatePriceNoAlloc(decoder));
    encoder.quantity(decoder.quantity());
    encoder.fee(calculateFeeNoAlloc(decoder));

    // No objects created, no GC pressure
}`
        });

        // Add more high-frequency trading specific optimizations
        performanceIssues.push({
          issue: "System time calls in critical path",
          impact: "High - System.currentTimeMillis() and System.nanoTime() can cause context switches (0.5-2μs overhead)",
          recommendation: "Use cached time or hardware timestamps",
          before: `
public long getTimestamp() {
    return System.nanoTime();
}`,
          after: `
// Option 1: Cached time updated by a dedicated thread
private static volatile long cachedNanoTime;
private static final Thread timeUpdateThread = new Thread(() -> {
    while (!Thread.currentThread().isInterrupted()) {
        cachedNanoTime = System.nanoTime();
        // Update every 100μs
        LockSupport.parkNanos(100_000);
    }
});

static {
    timeUpdateThread.setDaemon(true);
    timeUpdateThread.setPriority(Thread.MAX_PRIORITY);
    timeUpdateThread.setName("time-cache-thread");
    timeUpdateThread.start();
}

public long getTimestamp() {
    return cachedNanoTime;
}`,
          explanation: "System time calls can be expensive due to system calls and context switches. Cached time reduces overhead by 95-99% at the cost of slight precision loss.",
          advancedAlternative: `
// Option 2: Direct hardware timestamp counter (x86 only)
static {
    System.loadLibrary("rdtsc_native");
}

// Native method accessing the CPU's timestamp counter
private static native long rdtsc();

// Calibrate RDTSC to nanos
private static final double NANOS_PER_TICK = calibrateNanosPerTick();

private static double calibrateNanosPerTick() {
    long start = System.nanoTime();
    long startTicks = rdtsc();

    // Spin for 100ms
    long target = start + 100_000_000;
    while (System.nanoTime() < target) {
        Thread.onSpinWait();
    }

    long end = System.nanoTime();
    long endTicks = rdtsc();

    return (double)(end - start) / (endTicks - startTicks);
}

public long getTimestampNanos() {
    return (long)(rdtsc() * NANOS_PER_TICK);
}`
        });
      }

      // Database access optimizations
      if (code.includes("jdbc") || code.includes("ResultSet") || code.includes("PreparedStatement")) {
        performanceIssues.push({
          issue: "Inefficient JDBC usage",
          impact: "High - Excessive database roundtrips and connection management overhead",
          recommendation: "Use connection pooling and batch operations",
          before: `
for (Order order : orders) {
    try (Connection conn = DriverManager.getConnection(url, user, password);
         PreparedStatement stmt = conn.prepareStatement(
             "INSERT INTO orders (id, customer_id, amount) VALUES (?, ?, ?)")) {
        stmt.setLong(1, order.getId());
        stmt.setLong(2, order.getCustomerId());
        stmt.setBigDecimal(3, order.getAmount());
        stmt.executeUpdate();
    }
}`,
          after: `
// Use connection pooling
DataSource dataSource = setupConnectionPool();

// Batch operations
try (Connection conn = dataSource.getConnection();
     PreparedStatement stmt = conn.prepareStatement(
         "INSERT INTO orders (id, customer_id, amount) VALUES (?, ?, ?)")) {

    // Disable auto-commit for batching
    conn.setAutoCommit(false);

    int count = 0;
    final int BATCH_SIZE = 1000;

    for (Order order : orders) {
        stmt.setLong(1, order.getId());
        stmt.setLong(2, order.getCustomerId());
        stmt.setBigDecimal(3, order.getAmount());
        stmt.addBatch();

        if (++count % BATCH_SIZE == 0) {
            stmt.executeBatch();
        }
    }

    // Execute remaining batch
    if (count % BATCH_SIZE != 0) {
        stmt.executeBatch();
    }

    conn.commit();
}`,
          explanation: "Connection pooling eliminates connection establishment overhead (50-100ms per connection). Batch operations reduce network roundtrips and database parsing overhead by 10-100x for bulk operations.",
          advancedAlternative: `
// For maximum performance, use specialized JDBC drivers and direct memory
// Example with MariaDB's non-blocking connector

// Configure for zero-copy operations
MariaDbPoolDataSource pool = new MariaDbPoolDataSource();
pool.setUrl("****************************************************************************");
pool.setMaxPoolSize(10);

// Prepare once, reuse many times
String sql = "INSERT INTO orders (id, customer_id, amount) VALUES (?, ?, ?)";
MariaDbConnection conn = pool.getConnection().unwrap(MariaDbConnection.class);
ClientSidePreparedStatement pstmt = conn.prepareStatement(sql).unwrap(ClientSidePreparedStatement.class);

// Use direct memory buffers for batch data
DirectBuffer orderBuffer = new UnsafeBuffer(ByteBuffer.allocateDirect(orders.size() * 24));
int position = 0;

for (Order order : orders) {
    orderBuffer.putLong(position, order.getId());
    orderBuffer.putLong(position + 8, order.getCustomerId());
    orderBuffer.putDouble(position + 16, order.getAmount().doubleValue());
    position += 24;
}

// Execute with zero-copy from direct memory
pstmt.setDirectParameters(orderBuffer, orders.size());
int[] results = pstmt.executeBatch();`
        });
      }

      // Add more advanced optimizations based on context
      if (context.includes("microservice") || context.includes("web")) {
        performanceIssues.push({
          issue: "Blocking I/O in request handling",
          impact: "Critical - Limits throughput and wastes threads (10-100x throughput impact)",
          recommendation: "Use non-blocking I/O with reactive programming",
          before: `
@GetMapping("/orders/{customerId}")
public List<Order> getOrders(@PathVariable Long customerId) {
    return orderRepository.findByCustomerId(customerId);
}`,
          after: `
@GetMapping("/orders/{customerId}")
public Flux<Order> getOrders(@PathVariable Long customerId) {
    return orderRepository.findByCustomerIdReactive(customerId);
}`,
          explanation: "Reactive programming with non-blocking I/O allows handling 10-100x more concurrent requests with the same hardware by eliminating thread blocking during I/O operations.",
          advancedAlternative: `
// For ultra-high throughput, use specialized HTTP servers like Netty directly
public class HighPerformanceOrderServer {
    private final EventLoopGroup bossGroup = new NioEventLoopGroup(1);
    private final EventLoopGroup workerGroup = new NioEventLoopGroup();
    private final OrderRepository repository;

    // Configure with thread-per-core model and direct memory
    public void start() {
        ServerBootstrap b = new ServerBootstrap();
        b.group(bossGroup, workerGroup)
         .channel(NioServerSocketChannel.class)
         .childHandler(new HttpServerInitializer(repository))
         .option(ChannelOption.SO_BACKLOG, 128)
         .childOption(ChannelOption.SO_KEEPALIVE, true)
         .childOption(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT);

        ChannelFuture f = b.bind(8080).sync();
        f.channel().closeFuture().sync();
    }
}`
        });
      }

      // Create the result object
      const result = {
        issues: performanceIssues,
        summary: `Found ${performanceIssues.length} potential performance issues in your code.`,
        generalRecommendations: [
          "Profile before optimizing to identify actual bottlenecks using async-profiler or JMH",
          "Focus on algorithmic improvements before micro-optimizations (O(n²) → O(n log n) beats any constant factor)",
          "Measure performance impact of each change with statistical significance",
          "Consider mechanical sympathy - understand CPU caches, branch prediction, and memory access patterns",
          "For latency-critical code, eliminate all allocations in hot paths",
          "Use JVM flags to optimize for your specific workload (-XX:+UseZGC, -XX:+AlwaysPreTouch, etc.)"
        ]
      };

      // Store in cache for future identical requests
      analyzeJavaPerformanceCache.set(cacheKey, result);

      return result;
    },

    benchmarkJavaCode: ({ implementation, operationType = "mixed" }) => {
      // Fast template-based benchmark generation with operation type specialization
      const normalizedImpl = implementation.toLowerCase();
      const isReadOperation = operationType === "read";
      const isWriteOperation = operationType === "write";

      // Collection benchmarks
      if (normalizedImpl.includes("arraylist") && normalizedImpl.includes("linkedlist")) {
        // Customize benchmarks based on operation type
        const readBenchmarks = `
    @Benchmark
    public Integer arrayListGet() {
        return arrayList.get(index);
    }

    @Benchmark
    public Integer linkedListGet() {
        return linkedList.get(index);
    }

    @Benchmark
    public boolean arrayListContains() {
        return arrayList.contains(SIZE / 2);
    }

    @Benchmark
    public boolean linkedListContains() {
        return linkedList.contains(SIZE / 2);
    }`;

        const writeBenchmarks = `
    @Benchmark
    public boolean arrayListAdd() {
        return arrayList.add(SIZE);
    }

    @Benchmark
    public boolean linkedListAdd() {
        return linkedList.add(SIZE);
    }

    @Benchmark
    public boolean arrayListAddFirst() {
        arrayList.add(0, SIZE);
        return true;
    }

    @Benchmark
    public boolean linkedListAddFirst() {
        linkedList.add(0, SIZE);
        return true;
    }

    @Benchmark
    public Integer arrayListRemoveLast() {
        return arrayList.remove(arrayList.size() - 1);
    }

    @Benchmark
    public Integer linkedListRemoveLast() {
        return linkedList.remove(linkedList.size() - 1);
    }`;

        // Select benchmarks based on operation type
        let benchmarks = "";
        if (isReadOperation) {
          benchmarks = readBenchmarks;
        } else if (isWriteOperation) {
          benchmarks = writeBenchmarks;
        } else {
          // Mixed - include both read and write operations
          benchmarks = readBenchmarks + "\n" + writeBenchmarks;
        }

        return {
          benchmarkCode: `
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Thread)
@Fork(1)
@Warmup(iterations = 3, time = 1)
@Measurement(iterations = 5, time = 1)
public class ListBenchmark {
    private static final int SIZE = 10_000;
    private List<Integer> arrayList;
    private List<Integer> linkedList;
    private int index;

    @Setup
    public void setup() {
        arrayList = new ArrayList<>(SIZE);
        linkedList = new LinkedList<>();

        for (int i = 0; i < SIZE; i++) {
            arrayList.add(i);
            linkedList.add(i);
        }

        // For get operations - middle of list
        index = SIZE / 2;
    }

${benchmarks}

    public static void main(String[] args) throws Exception {
        Options opt = new OptionsBuilder()
                .include(ListBenchmark.class.getSimpleName())
                .build();
        new Runner(opt).run();
    }
}`,
          expectedResults: `
Benchmark                      Mode  Cnt     Score     Error  Units
ListBenchmark.arrayListGet     avgt    5     3.542 ±   0.124  ns/op
ListBenchmark.linkedListGet    avgt    5  1523.875 ± 145.231  ns/op
ListBenchmark.arrayListAdd     avgt    5    45.872 ±   5.231  ns/op
ListBenchmark.linkedListAdd    avgt    5    21.542 ±   1.231  ns/op
ListBenchmark.arrayListAddFirst avgt   5   985.321 ±  45.231  ns/op
ListBenchmark.linkedListAddFirst avgt  5    25.123 ±   2.123  ns/op`,
          analysis: `
Key findings:
1. ArrayList.get() is ~400x faster than LinkedList.get() for random access
2. LinkedList.add() at the end is ~2x faster than ArrayList.add()
3. LinkedList.add() at the beginning is ~40x faster than ArrayList.add(0, item)

Recommendation:
- Use ArrayList when random access is frequent
- Use LinkedList when frequently adding/removing elements at the beginning
- For most general-purpose use cases, ArrayList performs better due to memory locality and cache efficiency`
        };
      }

      // Locking benchmarks
      if (normalizedImpl.includes("lock") || normalizedImpl.includes("synchron")) {
        return {
          benchmarkCode: `
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Thread)
@Fork(1)
@Warmup(iterations = 3, time = 1)
@Measurement(iterations = 5, time = 1)
public class LockingBenchmark {
    private int counter = 0;
    private final Object mutex = new Object();
    private final Lock lock = new ReentrantLock();

    @Benchmark
    public int synchronizedMethod() {
        return incrementSynchronized();
    }

    @Benchmark
    public int synchronizedBlock() {
        synchronized(mutex) {
            return counter++;
        }
    }

    @Benchmark
    public int reentrantLock() {
        lock.lock();
        try {
            return counter++;
        } finally {
            lock.unlock();
        }
    }

    @Benchmark
    public int reentrantLockTryLock() {
        if (lock.tryLock()) {
            try {
                return counter++;
            } finally {
                lock.unlock();
            }
        }
        return counter; // Fallback if lock not acquired
    }

    private synchronized int incrementSynchronized() {
        return counter++;
    }

    public static void main(String[] args) throws Exception {
        Options opt = new OptionsBuilder()
                .include(LockingBenchmark.class.getSimpleName())
                .threads(4)
                .build();
        new Runner(opt).run();
    }
}`,
          expectedResults: `
Benchmark                           Mode  Cnt    Score    Error  Units
LockingBenchmark.synchronizedMethod avgt    5   25.321 ±  2.123  ns/op
LockingBenchmark.synchronizedBlock  avgt    5   24.875 ±  1.987  ns/op
LockingBenchmark.reentrantLock      avgt    5   21.542 ±  1.231  ns/op
LockingBenchmark.reentrantLockTryLock avgt 5   19.123 ±  0.987  ns/op`,
          analysis: `
Key findings:
1. ReentrantLock is ~15% faster than synchronized methods/blocks in low contention
2. ReentrantLock with tryLock() is the fastest option when lock acquisition usually succeeds
3. Under high contention (not shown), ReentrantLock's advantage increases to 30-50%

Recommendation:
- Use synchronized for simple, short-duration locking with low contention
- Use ReentrantLock for complex locking scenarios (timeouts, interruptibility, fairness)
- Consider lock-free alternatives (AtomicInteger, etc.) for even better performance
- For maximum performance, redesign to minimize shared mutable state`
        };
      }

      // Default benchmark template
      return {
        benchmarkCode: `
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.util.concurrent.TimeUnit;

@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Thread)
@Fork(1)
@Warmup(iterations = 3, time = 1)
@Measurement(iterations = 5, time = 1)
public class CustomBenchmark {
    // TODO: Define your test data structures here

    @Setup
    public void setup() {
        // TODO: Initialize your test data here
    }

    @Benchmark
    public Object implementation1() {
        // TODO: Implement first version
        return null;
    }

    @Benchmark
    public Object implementation2() {
        // TODO: Implement second version
        return null;
    }

    public static void main(String[] args) throws Exception {
        Options opt = new OptionsBuilder()
                .include(CustomBenchmark.class.getSimpleName())
                .build();
        new Runner(opt).run();
    }
}`,
        instructions: `
1. Replace the placeholder implementations with your specific code variants
2. Ensure both implementations return the same result type
3. Add appropriate @Param fields if you want to test with different input sizes
4. Run with: java -jar benchmarks.jar CustomBenchmark
5. Interpret results carefully - small differences may not be statistically significant
6. Consider running with different thread counts using .threads(N) in the OptionsBuilder`
      };
    },

    getJVMTuningParameters: ({ useCase, memoryConstraints = "8GB heap", gcPreference = "balanced" }) => {
      // Early return for empty use case
      if (!useCase || useCase.trim().length === 0) {
        return {
          jvmParameters: "-server -Xms1g -Xmx2g -XX:+UseG1GC",
          explanation: ["Default parameters provided - please specify a use case for optimized settings"],
          notes: ["Provide a specific use case for tailored JVM parameters"]
        };
      }

      // Fast lookup for optimized JVM parameters by use case
      const normalizedUseCase = useCase.toLowerCase();
      const normalizedMemory = memoryConstraints.toLowerCase();

      // Extract numeric memory value - optimized with single regex
      const memoryMatch = normalizedMemory.match(/(\d+)\s*(gb|mb|g|m)?/i);
      const memoryValue = memoryMatch ? parseInt(memoryMatch[1]) : 8;
      const memoryUnit = memoryMatch && memoryMatch[2] ? memoryMatch[2].toLowerCase() : (normalizedMemory.includes("gb") ? "gb" : "mb");
      const isGB = memoryUnit === "gb" || memoryUnit === "g";
      const memoryMB = isGB ? memoryValue * 1024 : memoryValue;

      // Base parameters for all configurations
      const basicParams = [
        "-server",
        `-Xms${Math.floor(memoryMB * 0.7)}m`,
        `-Xmx${memoryMB}m`,
        "-XX:+UseStringDeduplication",
        "-XX:+DisableExplicitGC",
        "-Djava.awt.headless=true"
      ];

      let gcParams: string[] = [];
      let advancedParams: string[] = [];
      let explanations: string[] = [];

      // GC selection based on preference and use case
      if (gcPreference === "latency" || normalizedUseCase.includes("low-latency") || normalizedUseCase.includes("trading")) {
        gcParams = [
          "-XX:+UseZGC",
          "-XX:ZCollectionInterval=5",
          "-XX:+UnlockExperimentalVMOptions",
          "-XX:+UseNUMA",
          "-XX:+AlwaysPreTouch"
        ];
        explanations.push("ZGC selected for consistent low latency with pause times under 10ms");
        explanations.push("Pre-touch ensures memory pages are allocated at startup, not during runtime");
        explanations.push("NUMA awareness improves memory access patterns on multi-socket servers");
      } else if (gcPreference === "throughput" || normalizedUseCase.includes("batch") || normalizedUseCase.includes("processing")) {
        gcParams = [
          "-XX:+UseParallelGC",
          `-XX:ParallelGCThreads=8`,
          `-XX:MaxGCPauseMillis=500`,
          "-XX:GCTimeRatio=19"
        ];
        explanations.push("Parallel GC selected for maximum throughput at the cost of pause times");
        explanations.push("GCTimeRatio=19 means spending 5% of time in GC (1/(1+19))");
      } else if (gcPreference === "footprint" || normalizedUseCase.includes("container") || normalizedUseCase.includes("cloud")) {
        gcParams = [
          "-XX:+UseSerialGC",
          "-XX:+UseStringDeduplication",
          "-XX:+UseCompressedOops",
          "-XX:+UseCompressedClassPointers"
        ];
        explanations.push("Serial GC selected for minimum memory footprint in constrained environments");
        explanations.push("Compressed Oops reduces heap usage by using 32-bit references instead of 64-bit");
      } else {
        // Balanced/default
        gcParams = [
          "-XX:+UseG1GC",
          `-XX:MaxGCPauseMillis=200`,
          `-XX:G1HeapRegionSize=${memoryMB > 4096 ? 8 : 4}m`,
          "-XX:+ParallelRefProcEnabled",
          "-XX:+UseStringDeduplication"
        ];
        explanations.push("G1GC selected for balanced throughput and latency");
        explanations.push("Target maximum pause time of 200ms balances responsiveness and throughput");
      }

      // Use case specific optimizations - using a map for O(1) lookup
      const useCaseParams = {
        microservice: {
          params: [
            "-XX:InitialRAMPercentage=70.0",
            "-XX:MaxRAMPercentage=75.0",
            "-XX:+ExitOnOutOfMemoryError",
            "-XX:+HeapDumpOnOutOfMemoryError"
          ],
          explanations: [
            "RAM percentage settings work better with containerized environments",
            "ExitOnOutOfMemoryError allows container orchestrators to restart the service"
          ]
        },
        container: {
          params: [
            "-XX:InitialRAMPercentage=70.0",
            "-XX:MaxRAMPercentage=75.0",
            "-XX:+ExitOnOutOfMemoryError",
            "-XX:+HeapDumpOnOutOfMemoryError"
          ],
          explanations: [
            "RAM percentage settings work better with containerized environments",
            "ExitOnOutOfMemoryError allows container orchestrators to restart the service"
          ]
        },
        trading: {
          params: [
            "-XX:+UseNUMA",
            "-XX:+UseBiasedLocking",
            "-XX:BiasedLockingStartupDelay=0",
            "-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider",
            "-XX:+AggressiveOpts"
          ],
          explanations: [
            "Biased locking improves performance for uncontended locks",
            "EPoll selector provides better scalability for NIO operations on Linux"
          ]
        },
        "low-latency": {
          params: [
            "-XX:+UseNUMA",
            "-XX:+UseBiasedLocking",
            "-XX:BiasedLockingStartupDelay=0",
            "-Djava.nio.channels.spi.SelectorProvider=sun.nio.ch.EPollSelectorProvider",
            "-XX:+AggressiveOpts"
          ],
          explanations: [
            "Biased locking improves performance for uncontended locks",
            "EPoll selector provides better scalability for NIO operations on Linux"
          ]
        },
        web: {
          params: [
            "-Dio.netty.allocator.type=pooled",
            "-Dio.netty.allocator.numDirectArenas=32",
            "-Djava.security.egd=file:/dev/urandom"
          ],
          explanations: [
            "Netty optimizations for web/API workloads",
            "Using /dev/urandom prevents blocking during secure random generation"
          ]
        },
        api: {
          params: [
            "-Dio.netty.allocator.type=pooled",
            "-Dio.netty.allocator.numDirectArenas=32",
            "-Djava.security.egd=file:/dev/urandom"
          ],
          explanations: [
            "Netty optimizations for web/API workloads",
            "Using /dev/urandom prevents blocking during secure random generation"
          ]
        }
      };

      // Find matching use cases - using type-safe approach
      for (const [key, value] of Object.entries(useCaseParams)) {
        if (normalizedUseCase.includes(key)) {
          advancedParams = [...advancedParams, ...value.params];
          explanations = [...explanations, ...value.explanations];
        }
      }

      // Combine all parameters
      const allParams = [...basicParams, ...gcParams, ...advancedParams];

      return {
        jvmParameters: allParams.join(" \\\n"),
        explanation: explanations,
        notes: [
          "Always benchmark these settings against your specific application",
          "Monitor GC logs to fine-tune parameters for your workload",
          "Consider using JVM ergonomics in containerized environments"
        ]
      };
    },

    analyzeReactPerformance: ({ code, context = "general" }) => {
      const performanceIssues = [];

      // React rendering optimization checks
      if (code.includes("useEffect") && !code.includes("dependencies")) {
        performanceIssues.push({
          issue: "Missing dependency array in useEffect",
          impact: "High - Causes unnecessary re-renders and potential memory leaks",
          recommendation: "Add dependency array to useEffect",
          before: `useEffect(() => {
  // effect code
});`,
          after: `useEffect(() => {
  // effect code
}, [/* dependencies */]);`,
          explanation: "Missing dependency arrays cause effects to run on every render, leading to performance degradation."
        });
      }

      // Component optimization patterns
      if (code.includes("map") && !code.includes("key={")) {
        performanceIssues.push({
          issue: "Missing key prop in list rendering",
          impact: "Medium - Forces React to re-render entire lists unnecessarily",
          recommendation: "Add unique key prop to list items",
          before: `{items.map(item => <Item item={item} />)}`,
          after: `{items.map(item => <Item key={item.id} item={item} />)}`,
          explanation: "Keys help React identify which items have changed, avoiding full list re-renders."
        });
      }

      // State management optimizations
      if (code.includes("useState") && !code.includes("useMemo") && !code.includes("useCallback")) {
        performanceIssues.push({
          issue: "Potential unnecessary re-renders",
          impact: "Medium - Component may re-render unnecessarily on state changes",
          recommendation: "Use memoization hooks for expensive computations and callbacks",
          before: `const expensiveValue = computeExpensiveValue(dep);
const handleClick = () => doSomething(dep);`,
          after: `const expensiveValue = useMemo(() => computeExpensiveValue(dep), [dep]);
const handleClick = useCallback(() => doSomething(dep), [dep]);`,
          explanation: "Memoization prevents expensive recalculations and stabilizes callback references."
        });
      }

      return {
        issues: performanceIssues,
        summary: performanceIssues.length > 0 ? "Found React.js performance optimization opportunities" : "No major React.js performance issues detected"
      };
    },

    analyzeDockerConfig: ({ config, type }) => {
      const performanceIssues = [];

      if (type === "dockerfile") {
        // Multi-stage build checks
        if (!config.includes("FROM") || !config.match(/FROM.*AS/)) {
          performanceIssues.push({
            issue: "No multi-stage builds",
            impact: "High - Larger image size and increased attack surface",
            recommendation: "Use multi-stage builds to minimize final image size",
            before: `FROM node:latest
COPY . .
RUN npm install
CMD ["npm", "start"]`,
            after: `# Build stage
FROM node:latest AS builder
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

# Production stage
FROM node:alpine
WORKDIR /app
COPY --from=builder /app/dist ./dist
COPY package*.json ./
RUN npm install --production
CMD ["npm", "start"]`,
            explanation: "Multi-stage builds reduce final image size by up to 90% by excluding build tools and dependencies."
          });
        }
      }

      return {
        issues: performanceIssues,
        summary: "Docker configuration analysis complete"
      };
    },

    analyzeKubernetesConfig: ({ manifest, resourceType }) => {
      const performanceIssues = [];

      if (resourceType === "deployment") {
        // Resource limits check
        if (!manifest.includes("resources:") || !manifest.includes("limits:")) {
          performanceIssues.push({
            issue: "Missing resource limits",
            impact: "Critical - Potential resource exhaustion and noisy neighbor issues",
            recommendation: "Add CPU and memory limits",
            before: `spec:
  containers:
  - name: app
    image: myapp:latest`,
            after: `spec:
  containers:
  - name: app
    image: myapp:latest
    resources:
      limits:
        cpu: "1"
        memory: "512Mi"
      requests:
        cpu: "200m"
        memory: "256Mi"`,
            explanation: "Resource limits prevent container resource exhaustion and improve cluster stability."
          });
        }
      }

      return {
        issues: performanceIssues,
        summary: "Kubernetes configuration analysis complete"
      };
    },

    analyzeGitWorkflow: ({ repoStats }) => {
      const performanceIssues = [];

      // Large repository optimizations
      if (repoStats.commitCount > 10000) {
        performanceIssues.push({
          issue: "Large repository without Git LFS",
          impact: "High - Slow clones and checkouts",
          recommendation: "Use Git LFS for large files",
          explanation: "Git LFS improves performance for large repositories by storing binary files separately."
        });
      }

      return {
        issues: performanceIssues,
        summary: "Git workflow analysis complete"
      };
    }
  }
};

export default [highPerformanceJavaExpert];
