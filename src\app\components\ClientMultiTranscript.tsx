"use client";

import React, { useEffect, useRef, useState, useCallback } from "react";
import { useMultiTranscript } from "@/app/contexts/MultiTranscriptContext";
import Transcript from "./Transcript";
import { TranscriptItem } from "@/app/types";

export interface MultiTranscriptProps {
  userText: string;
  setUserText: (val: string) => void;
  onSendMessage: () => void;
  canSend: boolean;
  downloadRecording: () => void;
  onClearTranscript?: () => void;
}

function ClientMultiTranscript({
  userText,
  setUserText,
  onSendMessage,
  canSend,
  downloadRecording,
  onClearTranscript,
}: MultiTranscriptProps) {
  const {
    transcriptBoxes,
    activeBoxId,
    setActiveBox,
    getTranscriptItems
  } = useMultiTranscript();

  // Track if the assistant is currently speaking in any box
  const [assistantSpeaking, setAssistantSpeaking] = useState<boolean>(false);

  // References to each transcript box
  const transcriptRefs = useRef<{[key: string]: HTMLDivElement | null}>({});

  // Function to register a transcript ref (memoized to prevent dependency array issues)
  const registerTranscriptRef = useCallback((boxId: string, ref: HTMLDivElement | null) => {
    transcriptRefs.current[boxId] = ref;
  }, []);

  // Function to scroll a specific box to the bottom (memoized to prevent dependency array issues)
  const scrollBoxToBottom = useCallback((boxId: string) => {
    console.log(`MultiTranscript: Attempting to scroll box ${boxId} to bottom`);
    const boxRef = transcriptRefs.current[boxId];
    if (boxRef) {
      // Check if user has manually scrolled up significantly
      const { scrollTop, scrollHeight, clientHeight } = boxRef;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
      const hasUserScrolledUp = distanceFromBottom > 200;

      // If user has manually scrolled up significantly, don't auto-scroll
      if (hasUserScrolledUp) {
        console.log(`MultiTranscript: Box ${boxId} - User has scrolled up, not auto-scrolling`);
        return;
      }

      const scrollToBottomHelper = (attempt = 0) => {
        if (boxRef) {
          // Force layout recalculation to get the latest scrollHeight
          const scrollHeight = boxRef.scrollHeight;
          const clientHeight = boxRef.clientHeight;

          console.log(`MultiTranscript: Box ${boxId} - Attempt ${attempt} - scrollHeight=${scrollHeight}, clientHeight=${clientHeight}`);

          try {
            // Use smooth scrolling for better user experience
            // Use a small timeout to ensure the DOM has updated
            setTimeout(() => {
              if (boxRef) {
                // Check again if user has scrolled up in the meantime
                const { scrollTop, scrollHeight, clientHeight } = boxRef;
                const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

                if (distanceFromBottom <= 200) {
                  // Force another layout recalculation
                  const forceLayout = boxRef.scrollHeight;

                  boxRef.scrollTo({
                    top: forceLayout,
                    behavior: 'smooth'
                  });

                  console.log(`MultiTranscript: Box ${boxId} - After scroll: scrollTop=${boxRef.scrollTop}, scrollHeight=${forceLayout}`);
                }
              }
            }, 10);
          } catch (e) {
            console.error(`Error scrolling box ${boxId}:`, e);
          }
        }
      };

      // Fewer attempts with increasing delays - be less aggressive
      scrollToBottomHelper(0);
      setTimeout(() => scrollToBottomHelper(1), 200);

      // One final attempt with a longer delay
      setTimeout(() => {
        if (boxRef) {
          // Check again if user has scrolled up in the meantime
          const { scrollTop, scrollHeight, clientHeight } = boxRef;
          const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

          if (distanceFromBottom <= 200) {
            try {
              // Force layout recalculation
              const forceLayout = boxRef.scrollHeight;

              boxRef.scrollTo({
                top: forceLayout,
                behavior: 'smooth'
              });

              console.log(`MultiTranscript: Box ${boxId} - Final scroll attempt`);
            } catch (e) {
              console.error(`Error during final scroll for box ${boxId}:`, e);
            }
          }
        }
      }, 500);
    }
  }, []);

  // Check if the assistant is speaking and handle scrolling
  useEffect(() => {
    const box = transcriptBoxes[0]; // Get the only box
    const items = box.transcriptItems;

    // Check if assistant is speaking
    const lastAssistantMessage = [...items]
      .reverse()
      .find(item => item.role === "assistant" && item.type === "MESSAGE");

    const isAssistantSpeaking = lastAssistantMessage && lastAssistantMessage.status === "IN_PROGRESS";
    setAssistantSpeaking(isAssistantSpeaking);

    // Scroll to bottom when new messages arrive or when assistant is speaking
    if (items.length > 0) {
      scrollBoxToBottom(box.id);

      // If assistant is speaking, set up a continuous scroll
      if (isAssistantSpeaking) {
        console.log("Assistant is speaking, setting up continuous scroll");

        // Scroll immediately
        scrollBoxToBottom(box.id);

        // Set up a smarter interval for scrolling during streaming that respects user scrolling
        let userHasScrolled = false;

        // Set up a scroll event listener to detect manual scrolling
        const detectUserScroll = () => {
          const boxRef = transcriptRefs.current[box.id];
          if (boxRef) {
            const { scrollTop, scrollHeight, clientHeight } = boxRef;
            // If we're more than 150px from the bottom, user has likely scrolled up manually
            if (scrollHeight - scrollTop - clientHeight > 150) {
              userHasScrolled = true;
              console.log("MultiTranscript: User has manually scrolled up, pausing auto-scroll");
            } else {
              // If user scrolls back to near bottom, resume auto-scrolling
              userHasScrolled = false;
            }
          }
        };

        const boxRef = transcriptRefs.current[box.id];
        if (boxRef) {
          boxRef.addEventListener('scroll', detectUserScroll);
        }

        // Set up a gentler interval that respects user scrolling
        const scrollInterval = setInterval(() => {
          const boxRef = transcriptRefs.current[box.id];
          if (boxRef) {
            // Only auto-scroll if user hasn't manually scrolled up
            if (!userHasScrolled) {
              boxRef.scrollTop = boxRef.scrollHeight;
            }
          } else {
            clearInterval(scrollInterval);
          }
        }, 300); // Less frequent checks

        // Clear the interval and remove event listener after a reasonable time
        setTimeout(() => {
          clearInterval(scrollInterval);
          const boxRef = transcriptRefs.current[box.id];
          if (boxRef) {
            boxRef.removeEventListener('scroll', detectUserScroll);
          }
        }, 10000);
      } else {
        // Just do a few scroll attempts for non-streaming updates
        setTimeout(() => scrollBoxToBottom(box.id), 100);
        setTimeout(() => scrollBoxToBottom(box.id), 300);
      }
    }
  }, [transcriptBoxes, scrollBoxToBottom]);

  // Handle sending a message (memoized to prevent dependency array issues)
  const handleSendMessage = useCallback(() => {
    // Since we only have one box now, we don't need to switch boxes
    // Just send the message
    onSendMessage();
  }, [onSendMessage]);

  // Since we only have one box, we can simplify the UI
  const box = transcriptBoxes[0]; // Get the only box

  return (
    <div className="flex flex-1 h-full overflow-hidden bg-gray-100">
      <div
        key={box.id}
        className="flex-1 overflow-hidden"
        style={{ minHeight: 0, display: 'flex', flexDirection: 'column', height: '100%' }}
        ref={(ref) => registerTranscriptRef(box.id, ref)}
      >
        <Transcript
          userText={userText}
          setUserText={setUserText}
          onSendMessage={handleSendMessage}
          canSend={canSend}
          downloadRecording={downloadRecording}
          transcriptItems={getTranscriptItems(box.id)}
          boxId={box.id}
          isActiveBox={true}
          parentScrollToBottom={() => scrollBoxToBottom(box.id)}
          onClearTranscript={onClearTranscript}
        />
      </div>
    </div>
  );
}

export default ClientMultiTranscript;
