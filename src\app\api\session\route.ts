import { NextResponse } from "next/server";
import { persistentSessionCache } from "@/app/lib/persistentCache";

// Cache key for the session
const SESSION_CACHE_KEY = "realtime-session";

export async function GET() {
  try {
    // Check if we have a cached session that's still valid
    const cachedSession = persistentSessionCache.get(SESSION_CACHE_KEY);
    if (cachedSession) {
      console.log("Using cached session");
      return NextResponse.json(cachedSession);
    }

    // If no valid cached session, request a new one
    console.log("Requesting new session from OpenAI");
    const response = await fetch(
      "https://api.openai.com/v1/realtime/sessions",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: "gpt-4o-realtime-preview-2024-12-17",
        }),
      }
    );

    const data = await response.json();

    // Cache the session data
    // Only cache successful responses
    if (data && data.client_secret && data.client_secret.value) {
      persistentSessionCache.set(SESSION_CACHE_KEY, data);
      // Force save to disk immediately to ensure persistence
      persistentSessionCache.saveToDisk();
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in /session:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
