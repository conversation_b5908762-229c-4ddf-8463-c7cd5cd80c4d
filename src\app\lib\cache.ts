/**
 * A simple in-memory cache with LRU (Least Recently Used) eviction policy
 */
export class LRUCache<K, V> {
  private cache = new Map<K, { value: V; timestamp: number }>();
  private readonly maxSize: number;
  private readonly ttl: number; // Time to live in milliseconds

  constructor(maxSize: number = 100, ttlMinutes: number = 60) {
    this.maxSize = maxSize;
    this.ttl = ttlMinutes * 60 * 1000; // Convert minutes to milliseconds
  }

  /**
   * Get a value from the cache
   * @param key The key to look up
   * @returns The cached value or undefined if not found or expired
   */
  get(key: K): V | undefined {
    const item = this.cache.get(key);

    if (!item) {
      return undefined;
    }

    // Check if the item has expired
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return undefined;
    }

    // Update the timestamp to mark as recently used
    item.timestamp = Date.now();
    this.cache.set(key, item);

    return item.value;
  }

  /**
   * Set a value in the cache
   * @param key The key to store
   * @param value The value to store
   */
  set(key: K, value: V): void {
    // If cache is at capacity, remove the least recently used item
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.findOldestKey();
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, { value, timestamp: Date.now() });
  }

  /**
   * Find the least recently used key
   * @returns The oldest key in the cache
   */
  private findOldestKey(): K | undefined {
    let oldestKey: K | undefined;
    let oldestTimestamp = Infinity;

    for (const [key, item] of this.cache.entries()) {
      if (item.timestamp < oldestTimestamp) {
        oldestTimestamp = item.timestamp;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  /**
   * Clear all items from the cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get the number of items in the cache
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Check if a key exists in the cache and is not expired
   * @param key The key to check
   */
  has(key: K): boolean {
    const item = this.cache.get(key);

    if (!item) {
      return false;
    }

    // Check if the item has expired
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }
}

// Create singleton instances for different cache types
// These in-memory caches are kept for backward compatibility
// For persistent caching, use the instances from persistentCache.ts
export const guardrailCache = new LRUCache<string, any>(500, 60); // 500 items, 60 minutes TTL
export const sessionCache = new LRUCache<string, any>(10, 5); // 10 items, 5 minutes TTL
export const completionsCache = new LRUCache<string, any>(200, 30); // 200 items, 30 minutes TTL

// Re-export persistent caches for convenience
import {
  persistentGuardrailCache,
  persistentSessionCache,
  persistentCompletionsCache
} from './persistentCache';

/**
 * Generate a cache key from a request body
 * @param body The request body to hash
 * @returns A string key
 */
export function generateCacheKey(body: any): string {
  // For simple objects, JSON.stringify with sorted keys works well as a cache key
  if (typeof body === 'object' && body !== null) {
    return JSON.stringify(sortObjectKeys(body));
  }

  // For strings, use as is
  if (typeof body === 'string') {
    return body;
  }

  // For other types, convert to string
  return String(body);
}

/**
 * Sort object keys recursively to ensure consistent cache keys
 * @param obj The object to sort
 * @returns A new object with sorted keys
 */
function sortObjectKeys(obj: any): any {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(sortObjectKeys);
  }

  return Object.keys(obj)
    .sort()
    .reduce((result: any, key) => {
      result[key] = sortObjectKeys(obj[key]);
      return result;
    }, {});
}
