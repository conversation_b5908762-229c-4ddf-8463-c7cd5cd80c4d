import { NextResponse } from "next/server";
import OpenA<PERSON> from "openai";
import { completionsCache, generateCacheKey } from "@/app/lib/cache";
import { persistentCompletionsCache } from "@/app/lib/persistentCache";

const openai = new OpenAI();

// List of models that are safe to cache (deterministic responses)
const CACHEABLE_MODELS = [
  "gpt-4o-mini",
  "gpt-3.5-turbo",
  "gpt-4-turbo",
];

// Check if a request is cacheable
function isRequestCacheable(body: any): boolean {
  // Only cache if it's a model we know is safe to cache
  if (!body.model || !CACHEABLE_MODELS.includes(body.model)) {
    return false;
  }

  // Don't cache if temperature is high (non-deterministic)
  if (body.temperature && body.temperature > 0.2) {
    return false;
  }

  // Don't cache if using top_p sampling (non-deterministic)
  if (body.top_p && body.top_p < 0.9) {
    return false;
  }

  // Don't cache streaming responses
  if (body.stream === true) {
    return false;
  }

  return true;
}

export async function POST(req: Request) {
  try {
    // Retrieve the entire JSON object from the request.
    const body = await req.json();

    // Check if this request is cacheable
    const cacheable = isRequestCacheable(body);

    if (cacheable) {
      // Generate a cache key based on the request body
      const cacheKey = generateCacheKey(body);

      // Check if we have a cached response
      const cachedResponse = completionsCache.get(cacheKey);
      if (cachedResponse) {
        console.log(`Using cached response for ${body.model}`);
        return NextResponse.json(cachedResponse);
      }
    }

    // If not cached or not cacheable, make the API call
    console.log(`Making API call to ${body.model}`);
    const completion = await openai.chat.completions.create({
      ...body,
    });

    // Cache the response if it's cacheable
    if (cacheable) {
      // Extract the user's question and the assistant's answer for better cache readability
      let userQuestion = "Unknown question";
      let assistantAnswer = "Unknown answer";

      try {
        // Extract the user's question from the request
        if (body.messages && Array.isArray(body.messages)) {
          const userMessages = body.messages.filter(m => m.role === "user");
          if (userMessages.length > 0) {
            const lastUserMessage = userMessages[userMessages.length - 1];
            userQuestion = typeof lastUserMessage.content === 'string'
              ? lastUserMessage.content
              : JSON.stringify(lastUserMessage.content);
          }
        }

        // Extract the assistant's answer from the response
        if (completion.choices && completion.choices.length > 0) {
          const firstChoice = completion.choices[0];
          if (firstChoice.message && firstChoice.message.content) {
            assistantAnswer = firstChoice.message.content;
          }
        }
      } catch (error) {
        console.error("Error extracting question/answer for cache:", error);
      }

      // Create a more descriptive cache key based on the question
      // Truncate long questions to keep keys manageable
      const truncatedQuestion = userQuestion.length > 50
        ? userQuestion.substring(0, 50) + "..."
        : userQuestion;

      // Create a timestamp-based unique identifier
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const cacheKey = `Q_${timestamp}_${truncatedQuestion.replace(/[^a-zA-Z0-9]/g, "_").substring(0, 30)}`;

      // Store the original cache key for reference
      const originalCacheKey = generateCacheKey(body);

      // Store enhanced information in the cache
      const enhancedCacheEntry = {
        ...completion,
        _cacheMetadata: {
          originalCacheKey,
          userQuestion,
          assistantAnswer,
          model: body.model,
          timestamp: Date.now()
        }
      };

      // Store in the persistent completions cache
      persistentCompletionsCache.set(cacheKey, enhancedCacheEntry);
    }

    return NextResponse.json(completion);
  } catch (error: any) {
    console.error("Error in /chat/completions:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
