"use client";

import React, { useState, useEffect } from "react";
import Detailed<PERSON>ache<PERSON>ie<PERSON> from "./DetailedCacheViewer";

interface CacheEntry {
  value: any;
  timestamp: number;
  formattedDate: string;
  expiresIn: number;
}

export default function SimpleCacheViewer() {
  const [cacheType, setCacheType] = useState<string>("guardrail");
  const [cacheContents, setCacheContents] = useState<Record<string, CacheEntry>>({});
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  // Fetch the contents of a specific cache
  const fetchCacheContents = async (type: string, addTestEntries: boolean = false) => {
    console.log(`Fetching cache contents for ${type}`);
    setLoading(true);

    try {
      // Only add test entries if explicitly requested
      if (addTestEntries) {
        await fetch(`/api/cache/test`);
        console.log("Added test entries to cache");
      }

      // Now fetch the cache contents
      const response = await fetch(`/api/cache/contents?type=${type}`);
      console.log(`Response status: ${response.status}`);

      if (response.ok) {
        const data = await response.json();
        console.log(`Cache contents:`, data);

        if (Object.keys(data).length === 0) {
          console.warn("No items returned from cache API");

          // Create a dummy entry for testing
          const dummyData = {
            "dummy-key": {
              value: "This is a dummy entry for testing",
              timestamp: Date.now(),
              formattedDate: new Date().toLocaleString(),
              expiresIn: 3600
            }
          };

          setCacheContents(dummyData);
        } else {
          setCacheContents(data);
        }
      } else {
        const error = await response.json();
        console.error(`Error response:`, error);
        setMessage(`Error: ${error.error}`);

        // Create a dummy entry for the error case
        const errorData = {
          "error-key": {
            value: `Error: ${error.error || "Unknown error"}`,
            timestamp: Date.now(),
            formattedDate: new Date().toLocaleString(),
            expiresIn: 3600
          }
        };

        setCacheContents(errorData);
      }
    } catch (error) {
      console.error("Error fetching cache contents:", error);
      setMessage(`Failed to fetch cache contents: ${error}`);

      // Create a dummy entry for the exception case
      const exceptionData = {
        "exception-key": {
          value: `Exception: ${error}`,
          timestamp: Date.now(),
          formattedDate: new Date().toLocaleString(),
          expiresIn: 3600
        }
      };

      setCacheContents(exceptionData);
    } finally {
      setLoading(false);
    }
  };

  // Force save all caches
  const saveAllCaches = async () => {
    setLoading(true);
    setMessage("");

    try {
      const response = await fetch("/api/cache?action=save");

      if (response.ok) {
        const data = await response.json();
        setMessage(data.message);
        // Refresh contents after saving without adding test entries
        fetchCacheContents(cacheType, false);
      } else {
        const error = await response.json();
        setMessage(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error("Error saving caches:", error);
      setMessage("Failed to save caches. See console for details.");
    } finally {
      setLoading(false);
    }
  };

  // Delete a specific cache entry
  const deleteCacheEntry = async (key: string) => {
    try {
      const encodedKey = encodeURIComponent(key);
      const response = await fetch(`/api/cache/contents?type=${cacheType}&key=${encodedKey}`, {
        method: "DELETE",
      });

      if (response.ok) {
        // Remove the entry from the local state
        const newContents = { ...cacheContents };
        delete newContents[key];
        setCacheContents(newContents);

        setMessage(`Entry removed from ${cacheType} cache`);
      } else {
        const error = await response.json();
        setMessage(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error("Error deleting cache entry:", error);
      setMessage(`Failed to delete cache entry: ${error}`);
    }
  };

  // Format time remaining until expiration
  const formatTimeRemaining = (seconds: number) => {
    if (seconds < 60) {
      return `${seconds}s`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}h ${minutes}m`;
    }
  };

  // Fetch cache contents when the component mounts or cache type changes
  useEffect(() => {
    fetchCacheContents(cacheType, false);
  }, [cacheType]);

  return (
    <div className="bg-white rounded-xl p-4 shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Simple Cache Viewer</h2>
        <div className="flex space-x-2">
          <select
            value={cacheType}
            onChange={(e) => setCacheType(e.target.value)}
            className="border border-gray-300 rounded px-2 py-1"
          >
            <option value="guardrail">Guardrail Cache</option>
            <option value="session">Session Cache</option>
            <option value="completions">Completions Cache</option>
          </select>
          <div className="flex space-x-2">
            <button
              onClick={() => fetchCacheContents(cacheType, false)}
              className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Refresh
            </button>
            <button
              onClick={saveAllCaches}
              className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Save Caches
            </button>
          </div>
        </div>
      </div>

      {message && (
        <div className="mb-4 p-2 bg-blue-50 text-blue-800 rounded text-sm">
          {message}
        </div>
      )}

      {loading ? (
        <div className="p-8 text-center text-gray-500">Loading cache contents...</div>
      ) : Object.keys(cacheContents).length === 0 ? (
        <div className="p-8 text-center text-gray-500">
          <p>No items in cache</p>
          <button
            onClick={() => fetchCacheContents(cacheType, true)}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Add Test Entries
          </button>
        </div>
      ) : (
        <div className="overflow-auto max-h-[500px]">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Key
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Used
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Expires In
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {Object.entries(cacheContents).map(([key, entry]) => (
                <tr key={key} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 max-w-xs truncate">
                    {key}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {entry.formattedDate}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatTimeRemaining(entry.expiresIn)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button
                      onClick={() => deleteCacheEntry(key)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="mt-4">
        <div className="mb-4">
          <h3 className="font-medium text-lg mb-2">Detailed Cache View</h3>
          <DetailedCacheViewer cacheContents={cacheContents} cacheType={cacheType} />
        </div>

        <div className="p-4 bg-gray-50 rounded">
          <h3 className="font-medium mb-2">Raw Cache Data</h3>
          <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-[200px]">
            {JSON.stringify(cacheContents, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
